<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class UserController extends Controller
{
    public function index(Request $request)
    {
        // Sample data users - nanti bisa diganti dengan data dari database
        $users = collect([
            [
                'id' => 1,
                'nama' => 'Siti Aminah',
                'email' => '<EMAIL>',
                'role' => 'UMKM',
                'status' => 'Aktif',
                'last_login' => '2024-07-25 10:30:00',
                'created_at' => '2024-01-15'
            ],
            [
                'id' => 2,
                'nama' => '<PERSON>i <PERSON>o',
                'email' => '<EMAIL>',
                'role' => 'UMKM',
                'status' => 'Aktif',
                'last_login' => '2024-07-24 15:45:00',
                'created_at' => '2024-02-10'
            ],
            [
                'id' => 3,
                'nama' => 'Admin PLUT',
                'email' => '<EMAIL>',
                'role' => 'Admin',
                'status' => 'Aktif',
                'last_login' => '2024-07-25 08:00:00',
                'created_at' => '2023-12-01'
            ]
        ]);

        return view('admin.users.index', compact('users'));
    }

    public function create()
    {
        return view('admin.users.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'nama' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'password' => 'required|string|min:8|confirmed',
            'role' => 'required|in:Admin,UMKM'
        ]);

        // Logic untuk menyimpan user
        return redirect()->route('admin.users.index')
            ->with('success', 'User berhasil ditambahkan!');
    }

    public function edit($id)
    {
        // Sample data - nanti ambil dari database
        $user = [
            'id' => $id,
            'nama' => 'Siti Aminah',
            'email' => '<EMAIL>',
            'role' => 'UMKM',
            'status' => 'Aktif'
        ];

        return view('admin.users.edit', compact('user'));
    }

    public function update(Request $request, $id)
    {
        $request->validate([
            'nama' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email,' . $id,
            'role' => 'required|in:Admin,UMKM',
            'status' => 'required|in:Aktif,Nonaktif'
        ]);

        // Logic untuk update user
        return redirect()->route('admin.users.index')
            ->with('success', 'User berhasil diperbarui!');
    }

    public function destroy($id)
    {
        // Logic untuk hapus user
        return redirect()->route('admin.users.index')
            ->with('success', 'User berhasil dihapus!');
    }

    public function admin()
    {
        // Daftar admin sistem
        $admins = collect([
            [
                'id' => 1,
                'nama' => 'Super Admin',
                'email' => '<EMAIL>',
                'role' => 'Super Admin',
                'permissions' => ['all'],
                'last_login' => '2024-07-25 08:00:00'
            ],
            [
                'id' => 2,
                'nama' => 'Admin PLUT',
                'email' => '<EMAIL>',
                'role' => 'Admin',
                'permissions' => ['umkm', 'pelatihan', 'berita'],
                'last_login' => '2024-07-24 16:30:00'
            ]
        ]);

        return view('admin.users.admin', compact('admins'));
    }

    public function roles()
    {
        // Kelola role dan permission
        $roles = [
            [
                'name' => 'Super Admin',
                'permissions' => ['all'],
                'description' => 'Akses penuh ke semua fitur sistem'
            ],
            [
                'name' => 'Admin',
                'permissions' => ['umkm', 'pelatihan', 'berita', 'galeri'],
                'description' => 'Kelola konten dan data UMKM'
            ],
            [
                'name' => 'UMKM',
                'permissions' => ['profile', 'pelatihan'],
                'description' => 'Akses terbatas untuk pelaku UMKM'
            ]
        ];

        return view('admin.users.roles', compact('roles'));
    }
}