<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Usaha extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'nama_usaha',
        'alama<PERSON>_usaha',
        'jenis_usaha',
        'klasif<PERSON><PERSON>',
    ];

    // <PERSON>lasi ke User (pemilik usaha)
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    // Relasi ke Legalitas
    public function legalitas()
    {
        return $this->hasMany(\App\Models\Legalitas::class, 'usaha_id');
    }
}
