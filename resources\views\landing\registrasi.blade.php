@extends('layouts.utama')
@section('judul', 'Daftar - PLUT Kabupaten Purworejo')
@section('deskripsi', 'Daftarkan UMKM Anda di PLUT Kabupaten Purworejo')

@section('konten')
<!-- Header <PERSON> -->
<section class="bg-gradient-to-r from-green-600 to-blue-600 text-white py-16">
    <div class="container mx-auto px-4 text-center">
        <h1 class="text-4xl md:text-5xl font-bold mb-4">Daftar UMKM</h1>
        <p class="text-xl">Bergabunglah dengan ribuan UMKM lainnya</p>
    </div>
</section>

<!-- Breadcrumb -->
<section class="bg-gray-100 py-4">
    <div class="container mx-auto px-4">
        <nav class="text-sm">
            <a href="{{ route('beranda') }}" class="text-plut-primary hover:underline">Beranda</a>
            <span class="mx-2 text-gray-500">></span>
            <span class="text-gray-700">Daftar</span>
        </nav>
    </div>
</section>

<!-- Progress Steps -->
<section class="py-8 bg-white">
    <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto">
            <div class="flex items-center justify-center space-x-8">
                <div class="flex items-center">
                    <div class="bg-plut-primary text-white w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold">1</div>
                    <span class="ml-2 text-sm font-semibold text-plut-primary">Data Pribadi</span>
                </div>
                <div class="flex-1 h-1 bg-gray-200 rounded"></div>
                <div class="flex items-center">
                    <div class="bg-gray-300 text-gray-600 w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold">2</div>
                    <span class="ml-2 text-sm text-gray-600">Data Usaha</span>
                </div>
                <div class="flex-1 h-1 bg-gray-200 rounded"></div>
                <div class="flex items-center">
                    <div class="bg-gray-300 text-gray-600 w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold">3</div>
                    <span class="ml-2 text-sm text-gray-600">Verifikasi</span>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Form Registrasi -->
<section class="py-16 bg-gray-50">
    <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto">
            <div class="bg-white rounded-xl shadow-lg overflow-hidden">
                <div class="p-8">
                    <div class="text-center mb-8">
                        <h2 class="text-2xl font-bold text-gray-800">Formulir Pendaftaran UMKM</h2>
                        <p class="text-gray-600 mt-2">Lengkapi data di bawah ini untuk mendaftarkan UMKM Anda</p>
                    </div>

                    <form class="space-y-8">
                        <!-- Data Pribadi -->
                        <div>
                            <h3 class="text-lg font-bold text-gray-800 mb-6 flex items-center">
                                <svg class="w-6 h-6 mr-2 text-plut-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                                Data Pribadi
                            </h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-semibold text-gray-700 mb-2">Nama Lengkap *</label>
                                    <input type="text" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-plut-primary focus:border-transparent transition duration-300" placeholder="Masukkan nama lengkap">
                                </div>
                                <div>
                                    <label class="block text-sm font-semibold text-gray-700 mb-2">NIK *</label>
                                    <input type="text" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-plut-primary focus:border-transparent transition duration-300" placeholder="Masukkan NIK">
                                </div>
                                <div>
                                    <label class="block text-sm font-semibold text-gray-700 mb-2">Email *</label>
                                    <input type="email" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-plut-primary focus:border-transparent transition duration-300" placeholder="Masukkan email">
                                </div>
                                <div>
                                    <label class="block text-sm font-semibold text-gray-700 mb-2">Nomor HP *</label>
                                    <input type="tel" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-plut-primary focus:border-transparent transition duration-300" placeholder="Masukkan nomor HP">
                                </div>
                                <div>
                                    <label class="block text-sm font-semibold text-gray-700 mb-2">Provinsi *</label>
                                    <select id="provinsi-pribadi" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-plut-primary focus:border-transparent transition duration-300">
                                        <option value="">Pilih Provinsi</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-semibold text-gray-700 mb-2">Kabupaten/Kota *</label>
                                    <select id="kabupaten-pribadi" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-plut-primary focus:border-transparent transition duration-300" disabled>
                                        <option value="">Pilih Kabupaten/Kota</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-semibold text-gray-700 mb-2">Kecamatan *</label>
                                    <select id="kecamatan-pribadi" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-plut-primary focus:border-transparent transition duration-300" disabled>
                                        <option value="">Pilih Kecamatan</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-semibold text-gray-700 mb-2">Desa/Kelurahan *</label>
                                    <select id="desa-pribadi" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-plut-primary focus:border-transparent transition duration-300" disabled>
                                        <option value="">Pilih Desa/Kelurahan</option>
                                    </select>
                                </div>
                                <div class="md:col-span-2">
                                    <label class="block text-sm font-semibold text-gray-700 mb-2">Alamat Lengkap *</label>
                                    <textarea rows="3" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-plut-primary focus:border-transparent transition duration-300" placeholder="Masukkan alamat lengkap (nama jalan, nomor rumah, RT/RW)"></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- Data Usaha -->
                        <div>
                            <h3 class="text-lg font-bold text-gray-800 mb-6 flex items-center">
                                <svg class="w-6 h-6 mr-2 text-plut-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                </svg>
                                Data Usaha
                            </h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-semibold text-gray-700 mb-2">Nama Usaha *</label>
                                    <input type="text" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-plut-primary focus:border-transparent transition duration-300" placeholder="Masukkan nama usaha">
                                </div>
                                <div>
                                    <label class="block text-sm font-semibold text-gray-700 mb-2">Nama Merk/Brand</label>
                                    <input type="text" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-plut-primary focus:border-transparent transition duration-300" placeholder="Masukkan nama merk (opsional)">
                                </div>
                                <div class="md:col-span-2">
                                    <label class="block text-sm font-semibold text-gray-700 mb-2">Bidang Usaha *</label>
                                    <select class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-plut-primary focus:border-transparent transition duration-300">
                                        <option>Pilih Bidang Usaha</option>
                                        <option>Makanan & Minuman</option>
                                        <option>Fashion & Tekstil</option>
                                        <option>Kerajinan Tangan</option>
                                        <option>Pertanian & Perkebunan</option>
                                        <option>Jasa</option>
                                        <option>Teknologi</option>
                                        <option>Lainnya</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-semibold text-gray-700 mb-2">Provinsi *</label>
                                    <select id="provinsi-usaha" class="w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-100 cursor-not-allowed" disabled>
                                        <option value="33">Jawa Tengah</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-semibold text-gray-700 mb-2">Kabupaten/Kota *</label>
                                    <select id="kabupaten-usaha" class="w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-100 cursor-not-allowed" disabled>
                                        <option value="3306">Purworejo</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-semibold text-gray-700 mb-2">Kecamatan *</label>
                                    <select id="kecamatan-usaha" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-plut-primary focus:border-transparent transition duration-300">
                                        <option value="">Pilih Kecamatan</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-semibold text-gray-700 mb-2">Desa/Kelurahan *</label>
                                    <select id="desa-usaha" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-plut-primary focus:border-transparent transition duration-300" disabled>
                                        <option value="">Pilih Desa/Kelurahan</option>
                                    </select>
                                </div>
                                <div class="md:col-span-2">
                                    <label class="block text-sm font-semibold text-gray-700 mb-2">Alamat Usaha *</label>
                                    <textarea rows="3" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-plut-primary focus:border-transparent transition duration-300" placeholder="Masukkan alamat usaha (nama jalan, nomor, RT/RW)"></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- Legalitas -->
                        <div>
                            <h3 class="text-lg font-bold text-gray-800 mb-6 flex items-center">
                                <svg class="w-6 h-6 mr-2 text-plut-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                Legalitas Usaha (Opsional)
                            </h3>
                            <div class="space-y-4">
                                <!-- NIB -->
                                <div>
                                    <div class="flex items-center mb-3">
                                        <input type="checkbox" id="nib-check" class="h-4 w-4 text-plut-primary focus:ring-plut-primary border-gray-300 rounded" onchange="toggleLegalitas('nib')">
                                        <label for="nib-check" class="ml-2 text-sm font-semibold text-gray-700">NIB (Nomor Induk Berusaha)</label>
                                    </div>
                                    <div id="nib-input" class="hidden">
                                        <input type="text" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-plut-primary focus:border-transparent transition duration-300" placeholder="Masukkan nomor NIB">
                                    </div>
                                </div>

                                <!-- NPWP -->
                                <div>
                                    <div class="flex items-center mb-3">
                                        <input type="checkbox" id="npwp-check" class="h-4 w-4 text-plut-primary focus:ring-plut-primary border-gray-300 rounded" onchange="toggleLegalitas('npwp')">
                                        <label for="npwp-check" class="ml-2 text-sm font-semibold text-gray-700">NPWP</label>
                                    </div>
                                    <div id="npwp-input" class="hidden">
                                        <input type="text" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-plut-primary focus:border-transparent transition duration-300" placeholder="Masukkan nomor NPWP">
                                    </div>
                                </div>

                                <!-- Sertifikat Halal -->
                                <div>
                                    <div class="flex items-center mb-3">
                                        <input type="checkbox" id="halal-check" class="h-4 w-4 text-plut-primary focus:ring-plut-primary border-gray-300 rounded" onchange="toggleLegalitas('halal')">
                                        <label for="halal-check" class="ml-2 text-sm font-semibold text-gray-700">Sertifikat Halal</label>
                                    </div>
                                    <div id="halal-input" class="hidden">
                                        <input type="text" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-plut-primary focus:border-transparent transition duration-300" placeholder="Masukkan nomor sertifikat halal">
                                    </div>
                                </div>

                                <!-- SIUP -->
                                <div>
                                    <div class="flex items-center mb-3">
                                        <input type="checkbox" id="siup-check" class="h-4 w-4 text-plut-primary focus:ring-plut-primary border-gray-300 rounded" onchange="toggleLegalitas('siup')">
                                        <label for="siup-check" class="ml-2 text-sm font-semibold text-gray-700">SIUP (Surat Izin Usaha Perdagangan)</label>
                                    </div>
                                    <div id="siup-input" class="hidden">
                                        <input type="text" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-plut-primary focus:border-transparent transition duration-300" placeholder="Masukkan nomor SIUP">
                                    </div>
                                </div>

                                <!-- Legalitas Lainnya -->
                                <div>
                                    <div class="flex items-center mb-3">
                                        <input type="checkbox" id="lainnya-check" class="h-4 w-4 text-plut-primary focus:ring-plut-primary border-gray-300 rounded" onchange="toggleLegalitas('lainnya')">
                                        <label for="lainnya-check" class="ml-2 text-sm font-semibold text-gray-700">Legalitas Lainnya</label>
                                    </div>
                                    <div id="lainnya-input" class="hidden space-y-3">
                                        <input type="text" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-plut-primary focus:border-transparent transition duration-300" placeholder="Masukkan nama legalitas (contoh: TDP, BPOM, dll)">
                                        <input type="text" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-plut-primary focus:border-transparent transition duration-300" placeholder="Masukkan nomor legalitas">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Akun -->
                        <div>
                            <h3 class="text-lg font-bold text-gray-800 mb-6 flex items-center">
                                <svg class="w-6 h-6 mr-2 text-plut-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                                </svg>
                                Keamanan Akun
                            </h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-semibold text-gray-700 mb-2">Password *</label>
                                    <input type="password" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-plut-primary focus:border-transparent transition duration-300" placeholder="Masukkan password">
                                    <p class="text-xs text-gray-500 mt-1">Minimal 8 karakter, kombinasi huruf dan angka</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-semibold text-gray-700 mb-2">Konfirmasi Password *</label>
                                    <input type="password" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-plut-primary focus:border-transparent transition duration-300" placeholder="Konfirmasi password">
                                </div>
                            </div>
                        </div>

                        <!-- Persetujuan -->
                        <div class="border-t pt-6">
                            <div class="space-y-4">
                                <div class="flex items-start">
                                    <input type="checkbox" class="mt-1 h-4 w-4 text-plut-primary focus:ring-plut-primary border-gray-300 rounded">
                                    <label class="ml-3 text-sm text-gray-700">
                                        Saya menyetujui <a href="#" class="text-plut-primary hover:underline">Syarat dan Ketentuan</a> yang berlaku
                                    </label>
                                </div>
                                <div class="flex items-start">
                                    <input type="checkbox" class="mt-1 h-4 w-4 text-plut-primary focus:ring-plut-primary border-gray-300 rounded">
                                    <label class="ml-3 text-sm text-gray-700">
                                        Saya menyetujui <a href="#" class="text-plut-primary hover:underline">Kebijakan Privasi</a> dan penggunaan data
                                    </label>
                                </div>
                                <div class="flex items-start">
                                    <input type="checkbox" class="mt-1 h-4 w-4 text-plut-primary focus:ring-plut-primary border-gray-300 rounded">
                                    <label class="ml-3 text-sm text-gray-700">
                                        Saya ingin menerima informasi pelatihan dan program PLUT melalui email
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="flex flex-col sm:flex-row gap-4 pt-6">
                            <a href="{{ route('login') }}" class="flex-1 bg-gray-200 text-gray-700 py-3 px-6 rounded-lg hover:bg-gray-300 transition duration-300 font-semibold text-center flex items-center justify-center">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                                </svg>
                                Kembali ke Login
                            </a>
                            <button type="submit" class="flex-1 bg-plut-primary text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition duration-300 font-semibold flex items-center justify-center">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                Daftar Sekarang
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Footer Card -->
                <div class="px-8 py-4 bg-gray-50 border-t">
                    <p class="text-center text-sm text-gray-600">
                        Sudah punya akun? 
                        <a href="{{ route('login') }}" class="text-plut-primary font-semibold hover:underline">Masuk di sini</a>
                    </p>
                </div>
            </div>

            <!-- Info Bantuan -->
            <div class="mt-8">
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                    <div class="flex items-start">
                        <svg class="w-6 h-6 text-blue-500 mr-3 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <div>
                            <h4 class="font-semibold text-blue-800 mb-2">Butuh Bantuan?</h4>
                            <p class="text-sm text-blue-700 mb-3">
                                Jika Anda mengalami kesulitan dalam pengisian formulir, silakan hubungi tim support kami:
                            </p>
                            <div class="space-y-1 text-sm text-blue-700">
                                <p>📞 WhatsApp: +62 812-3456-7890</p>
                                <p>📧 Email: <EMAIL></p>
                                <p>🕒 Senin - Jumat: 08:00 - 16:00</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
// API Wilayah Indonesia
const API_BASE = 'https://www.emsifa.com/api-wilayah-indonesia/api';

// Load kecamatan Purworejo dari API menggunakan ID kabupaten 3306
async function loadKecamatanPurworejo() {
    try {
        console.log('Loading kecamatan Purworejo from API with ID: 3306');
        const response = await fetch(`${API_BASE}/districts/3306.json`);
        const districts = await response.json();
        const select = document.getElementById('kecamatan-usaha');
        
        console.log('Kecamatan loaded from API:', districts.length, 'districts');
        
        // Clear previous options
        select.innerHTML = '<option value="">Pilih Kecamatan</option>';
        
        districts.forEach(district => {
            const option = document.createElement('option');
            option.value = district.id;
            option.textContent = district.name;
            select.appendChild(option);
        });
        
        console.log('Kecamatan Purworejo loaded successfully from API');
    } catch (error) {
        console.error('Error loading kecamatan Purworejo from API:', error);
        
        // Fallback ke data hardcoded jika API gagal
        const kecamatanFallback = [
            { id: '330601', name: 'Bagelen' },
            { id: '330602', name: 'Banyuurip' },
            { id: '330603', name: 'Bayan' },
            { id: '330604', name: 'Bruno' },
            { id: '330605', name: 'Butuh' },
            { id: '330606', name: 'Gebang' },
            { id: '330607', name: 'Grabag' },
            { id: '330608', name: 'Kaligesing' },
            { id: '330609', name: 'Kemiri' },
            { id: '330610', name: 'Kutoarjo' },
            { id: '330611', name: 'Loano' },
            { id: '330612', name: 'Ngombol' },
            { id: '330613', name: 'Pituruh' },
            { id: '330614', name: 'Purwodadi' },
            { id: '330615', name: 'Purworejo' },
            { id: '330616', name: 'Sucen' }
        ];
        
        const select = document.getElementById('kecamatan-usaha');
        select.innerHTML = '<option value="">Pilih Kecamatan</option>';
        
        kecamatanFallback.forEach(kecamatan => {
            const option = document.createElement('option');
            option.value = kecamatan.id;
            option.textContent = kecamatan.name;
            select.appendChild(option);
        });
        
        console.log('Using fallback kecamatan data');
    }
}

// Load Provinsi untuk alamat pribadi
async function loadProvinsi() {
    try {
        const response = await fetch(`${API_BASE}/provinces.json`);
        const provinces = await response.json();
        const select = document.getElementById('provinsi-pribadi');
        
        provinces.forEach(province => {
            const option = document.createElement('option');
            option.value = province.id;
            option.textContent = province.name;
            select.appendChild(option);
        });
    } catch (error) {
        console.error('Error loading provinces:', error);
    }
}

// Load Kabupaten
async function loadKabupaten(provinceId, targetSelect, nextSelect) {
    try {
        const response = await fetch(`${API_BASE}/regencies/${provinceId}.json`);
        const regencies = await response.json();
        const select = document.getElementById(targetSelect);
        
        // Clear previous options
        select.innerHTML = '<option value="">Pilih Kabupaten/Kota</option>';
        select.disabled = false;
        
        // Clear next selects
        if (nextSelect) {
            document.getElementById(nextSelect).innerHTML = '<option value="">Pilih Kecamatan</option>';
            document.getElementById(nextSelect).disabled = true;
        }
        
        regencies.forEach(regency => {
            const option = document.createElement('option');
            option.value = regency.id;
            option.textContent = regency.name;
            select.appendChild(option);
        });
    } catch (error) {
        console.error('Error loading regencies:', error);
    }
}

// Load Kecamatan
async function loadKecamatan(regencyId, targetSelect, nextSelect) {
    try {
        const response = await fetch(`${API_BASE}/districts/${regencyId}.json`);
        const districts = await response.json();
        const select = document.getElementById(targetSelect);
        
        // Clear previous options
        select.innerHTML = '<option value="">Pilih Kecamatan</option>';
        select.disabled = false;
        
        // Clear next select
        if (nextSelect) {
            document.getElementById(nextSelect).innerHTML = '<option value="">Pilih Desa/Kelurahan</option>';
            document.getElementById(nextSelect).disabled = true;
        }
        
        districts.forEach(district => {
            const option = document.createElement('option');
            option.value = district.id;
            option.textContent = district.name;
            select.appendChild(option);
        });
    } catch (error) {
        console.error('Error loading districts:', error);
    }
}

// Load Desa
async function loadDesa(districtId, targetSelect) {
    try {
        console.log('Loading desa for district:', districtId, 'target:', targetSelect); // Debug log
        const response = await fetch(`${API_BASE}/villages/${districtId}.json`);
        const villages = await response.json();
        const select = document.getElementById(targetSelect);
        
        console.log('Villages loaded:', villages.length, 'villages'); // Debug log
        
        // Clear previous options
        select.innerHTML = '<option value="">Pilih Desa/Kelurahan</option>';
        select.disabled = false;
        
        villages.forEach(village => {
            const option = document.createElement('option');
            option.value = village.id;
            option.textContent = village.name;
            select.appendChild(option);
        });
        
        console.log('Dropdown desa updated, disabled:', select.disabled); // Debug log
    } catch (error) {
        console.error('Error loading villages:', error);
    }
}

// Load Desa khusus untuk usaha
async function loadDesaUsaha(districtId) {
    try {
        console.log('Loading desa usaha for district:', districtId); // Debug log
        const response = await fetch(`${API_BASE}/villages/${districtId}.json`);
        const villages = await response.json();
        const select = document.getElementById('desa-usaha');
        
        console.log('Villages loaded for usaha:', villages.length, 'villages'); // Debug log
        
        // Clear previous options
        select.innerHTML = '<option value="">Pilih Desa/Kelurahan</option>';
        
        // Pastikan dropdown aktif
        select.disabled = false;
        select.removeAttribute('disabled');
        
        villages.forEach(village => {
            const option = document.createElement('option');
            option.value = village.id;
            option.textContent = village.name;
            select.appendChild(option);
        });
        
        console.log('Dropdown desa usaha updated, disabled:', select.disabled); // Debug log
        console.log('Dropdown desa usaha element:', select); // Debug log
    } catch (error) {
        console.error('Error loading villages for usaha:', error);
    }
}


// Event listeners untuk alamat pribadi
document.getElementById('provinsi-pribadi').addEventListener('change', function() {
    if (this.value) {
        loadKabupaten(this.value, 'kabupaten-pribadi', 'kecamatan-pribadi');
    }
});

document.getElementById('kabupaten-pribadi').addEventListener('change', function() {
    if (this.value) {
        loadKecamatan(this.value, 'kecamatan-pribadi', 'desa-pribadi');
    }
});

document.getElementById('kecamatan-pribadi').addEventListener('change', function() {
    if (this.value) {
        loadDesa(this.value, 'desa-pribadi');
    }
});

// Event listeners untuk alamat usaha
document.getElementById('kecamatan-usaha').addEventListener('change', function() {
    console.log('Kecamatan usaha dipilih:', this.value); // Debug log
    if (this.value) {
        loadDesaUsaha(this.value); // Gunakan fungsi khusus untuk usaha
    } else {
        // Reset desa dropdown jika kecamatan tidak dipilih
        const desaSelect = document.getElementById('desa-usaha');
        desaSelect.innerHTML = '<option value="">Pilih Desa/Kelurahan</option>';
        desaSelect.disabled = true;
    }
});

// Toggle legalitas input
function toggleLegalitas(type) {
    const checkbox = document.getElementById(`${type}-check`);
    const input = document.getElementById(`${type}-input`);
    
    if (checkbox.checked) {
        input.classList.remove('hidden');
    } else {
        input.classList.add('hidden');
    }
}

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    loadProvinsi();
    loadKecamatanPurworejo();
});
</script>
@endsection