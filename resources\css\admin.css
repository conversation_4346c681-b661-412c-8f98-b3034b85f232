/* Admin Dashboard Custom Styles */

/* PLUT Color Variables */
:root {
    --plut-primary: #3b82f6;
    --plut-secondary: #1e40af;
    --plut-accent: #60a5fa;
}

/* Custom PLUT Classes */
.bg-plut-primary {
    background-color: var(--plut-primary);
}

.bg-plut-secondary {
    background-color: var(--plut-secondary);
}

.text-plut-primary {
    color: var(--plut-primary);
}

.border-plut-primary {
    border-color: var(--plut-primary);
}

.hover\:bg-plut-primary:hover {
    background-color: var(--plut-primary);
}

.focus\:ring-plut-primary:focus {
    --tw-ring-color: var(--plut-primary);
}

.focus\:border-plut-primary:focus {
    border-color: var(--plut-primary);
}

/* Card Styles */
.kartu-konten {
    @apply bg-white rounded-lg shadow-md p-6;
}

/* Button Styles */
.btn-primary {
    @apply bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors duration-200;
}

.btn-secondary {
    @apply bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors duration-200;
}

.btn-success {
    @apply bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors duration-200;
}

.btn-danger {
    @apply bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors duration-200;
}

.btn-warning {
    @apply bg-yellow-600 text-white px-4 py-2 rounded-lg hover:bg-yellow-700 transition-colors duration-200;
}

/* Form Styles */
.form-input {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500;
}

.form-select {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500;
}

.form-textarea {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500;
}

.form-label {
    @apply block text-sm font-medium text-gray-700 mb-2;
}

/* Table Styles */
.table-responsive {
    @apply overflow-x-auto bg-white rounded-lg shadow-md;
}

.table-header {
    @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50;
}

.table-cell {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
}

.table-row {
    @apply hover:bg-gray-50 transition-colors duration-150;
}

/* Status Badge Styles */
.badge {
    @apply px-2 inline-flex text-xs leading-5 font-semibold rounded-full;
}

.badge-success {
    @apply bg-green-100 text-green-800;
}

.badge-warning {
    @apply bg-yellow-100 text-yellow-800;
}

.badge-danger {
    @apply bg-red-100 text-red-800;
}

.badge-info {
    @apply bg-blue-100 text-blue-800;
}

.badge-secondary {
    @apply bg-gray-100 text-gray-800;
}

/* Statistics Card Styles */
.stat-card {
    @apply bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow duration-300;
}

.stat-icon {
    @apply p-3 rounded-full;
}

.stat-icon-blue {
    @apply bg-blue-100 text-blue-600;
}

.stat-icon-green {
    @apply bg-green-100 text-green-600;
}

.stat-icon-yellow {
    @apply bg-yellow-100 text-yellow-600;
}

.stat-icon-red {
    @apply bg-red-100 text-red-600;
}

.stat-icon-purple {
    @apply bg-purple-100 text-purple-600;
}

/* Sidebar Styles */
.sidebar-link {
    @apply flex items-center px-4 py-3 text-gray-700 rounded-lg hover:bg-blue-600 hover:text-white transition-colors duration-200;
}

.sidebar-link.active {
    @apply bg-blue-600 text-white;
}

.sidebar-submenu {
    @apply ml-8 mt-2 space-y-2;
}

.sidebar-submenu-link {
    @apply flex items-center px-4 py-2 text-sm text-gray-600 rounded-lg hover:bg-blue-600 hover:text-white transition-colors duration-200;
}

.sidebar-submenu-link.active {
    @apply bg-blue-600 text-white;
}

/* Page Header Styles */
.page-header {
    @apply bg-white rounded-lg shadow-md p-6 mb-6;
}

.page-title {
    @apply text-2xl font-bold text-gray-900;
}

.page-subtitle {
    @apply text-gray-600 mt-1;
}

/* Filter Section Styles */
.filter-section {
    @apply bg-white rounded-lg shadow-md p-6;
}

.filter-title {
    @apply text-lg font-semibold text-gray-900 mb-4;
}

.filter-grid {
    @apply grid grid-cols-1 md:grid-cols-4 gap-4;
}

.filter-actions {
    @apply flex justify-between items-center mt-4;
}

/* Action Button Styles */
.action-btn {
    @apply p-2 rounded hover:bg-gray-100 transition-colors duration-150;
}

.action-btn-primary {
    @apply text-blue-600 hover:text-blue-700;
}

.action-btn-success {
    @apply text-green-600 hover:text-green-700;
}

.action-btn-warning {
    @apply text-yellow-600 hover:text-yellow-700;
}

.action-btn-danger {
    @apply text-red-600 hover:text-red-700;
}

/* Notification Styles */
.notification {
    @apply px-4 py-3 rounded relative mb-6;
}

.notification-success {
    @apply bg-green-100 border border-green-400 text-green-700;
}

.notification-error {
    @apply bg-red-100 border border-red-400 text-red-700;
}

.notification-warning {
    @apply bg-yellow-100 border border-yellow-400 text-yellow-700;
}

.notification-info {
    @apply bg-blue-100 border border-blue-400 text-blue-700;
}

/* Loading Styles */
.loading-spinner {
    @apply animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600;
}

/* Responsive Utilities */
@media (max-width: 768px) {
    .mobile-hidden {
        display: none;
    }
    
    .mobile-full {
        width: 100%;
    }
    
    .mobile-stack {
        flex-direction: column;
    }
}

/* Dark Mode Support (Future) */
@media (prefers-color-scheme: dark) {
    .dark-mode {
        /* Dark mode styles will be added here */
    }
}

/* Print Styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    .print-only {
        display: block !important;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in {
    animation: slideIn 0.3s ease-in-out;
}

@keyframes slideIn {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

/* Custom Scrollbar */
.custom-scrollbar::-webkit-scrollbar {
    width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}