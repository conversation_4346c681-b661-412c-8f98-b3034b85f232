<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Usaha;
use Illuminate\Http\Request;

class UmkmController extends Controller
{
    public function index(Request $request)
    {
        $query = Usaha::with(['user.profil', 'legalitas']);

        // Filter berdasarkan request
        if ($request->filled('search')) {
            $query->where(function($q) use ($request) {
                $q->where('nama_usaha', 'like', '%' . $request->search . '%')
                  ->orWhere('nama_merk', 'like', '%' . $request->search . '%')
                  ->orWhereHas('user', function($userQuery) use ($request) {
                      $userQuery->where('nama', 'like', '%' . $request->search . '%');
                  });
            });
        }

        if ($request->filled('kategori')) {
            $query->where('bidang_usaha', $request->kategori);
        }

        if ($request->filled('status')) {
            $query->where('status_verifikasi', $request->status);
        }

        if ($request->filled('kecamatan')) {
            $query->where('kecamatan', $request->kecamatan);
        }

        $umkms = $query->paginate(10);

        return view('admin.umkm.index', compact('umkms'));
    }

    public function create()
    {
        return view('admin.umkm.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'nama_usaha' => 'required|string|max:255',
            'nama_merk' => 'required|string|max:255',
            'bidang_usaha' => 'required|string|max:255',
            'alamat_lengkap' => 'required|string',
            'deskripsi' => 'nullable|string'
        ]);

        // Logic untuk menyimpan UMKM
        // Untuk sekarang redirect dengan pesan sukses

        return redirect()->route('admin.umkm.index')
            ->with('success', 'UMKM berhasil ditambahkan!');
    }

    public function show($id)
    {
        $umkm = Usaha::with(['user.profil', 'legalitas'])->findOrFail($id);
        return view('admin.umkm.show', compact('umkm'));
    }

    public function edit($id)
    {
        $umkm = Usaha::with(['user.profil', 'legalitas'])->findOrFail($id);
        return view('admin.umkm.edit', compact('umkm'));
    }

    public function update(Request $request, $id)
    {
        $request->validate([
            'nama_usaha' => 'required|string|max:255',
            'nama_merk' => 'required|string|max:255',
            'bidang_usaha' => 'required|string|max:255',
            'alamat_lengkap' => 'required|string',
            'deskripsi' => 'nullable|string'
        ]);

        // Logic untuk update UMKM
        // Untuk sekarang redirect dengan pesan sukses

        return redirect()->route('admin.umkm.index')
            ->with('success', 'UMKM berhasil diperbarui!');
    }

    public function destroy($id)
    {
        // Logic untuk hapus UMKM
        // Untuk sekarang redirect dengan pesan sukses

        return redirect()->route('admin.umkm.index')
            ->with('success', 'UMKM berhasil dihapus!');
    }

    public function pendaftaran()
    {
        // UMKM yang baru mendaftar dan belum diverifikasi
        $umkmBaru = Usaha::with(['user.profil'])
            ->where('status_verifikasi', 'pending')
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('admin.umkm.pendaftaran', compact('umkmBaru'));
    }

    public function kategori()
    {
        // Kelola kategori UMKM
        $kategori = [
            'Makanan & Minuman',
            'Kerajinan',
            'Fashion',
            'Jasa',
            'Teknologi',
            'Pertanian',
            'Perdagangan'
        ];

        return view('admin.umkm.kategori', compact('kategori'));
    }

    public function verifikasi()
    {
        // UMKM yang perlu diverifikasi
        $umkmVerifikasi = Usaha::with(['user.profil', 'legalitas'])
            ->whereIn('status_verifikasi', ['pending', 'review'])
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('admin.umkm.verifikasi', compact('umkmVerifikasi'));
    }

    public function verify($id)
    {
        $umkm = Usaha::findOrFail($id);
        $umkm->update(['status_verifikasi' => 'verified']);

        return redirect()->back()
            ->with('success', 'UMKM berhasil diverifikasi!');
    }

    public function reject(Request $request, $id)
    {
        $request->validate([
            'alasan' => 'required|string'
        ]);

        $umkm = Usaha::findOrFail($id);
        $umkm->update([
            'status_verifikasi' => 'rejected',
            'alasan_penolakan' => $request->alasan
        ]);

        return redirect()->back()
            ->with('success', 'UMKM berhasil ditolak!');
    }
}
