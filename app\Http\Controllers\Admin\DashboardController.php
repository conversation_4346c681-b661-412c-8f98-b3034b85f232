<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class DashboardController extends Controller
{
    public function index()
    {
        // Data statistik untuk dashboard
        $stats = [
            'total_umkm' => 1250,
            'umkm_aktif' => 1156,
            'pelatihan_total' => 85,
            'pendaftaran_pending' => 24,
            'peserta_pelatihan' => 2400,
            'tingkat_kepuasan' => 95
        ];

        // Data untuk chart UMKM per bulan
        $umkmPerBulan = [
            'labels' => ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
            'data' => [65, 78, 90, 81, 95, 105, 110, 125, 140, 155, 170, 185]
        ];

        // Data untuk chart kategori UMKM
        $kategoriUmkm = [
            'labels' => ['<PERSON><PERSON><PERSON> & <PERSON>uman', 'Kerajinan', 'Fashion', '<PERSON><PERSON>', 'Teknologi', 'Lainnya'],
            'data' => [450, 320, 280, 150, 80, 70]
        ];

        // Aktivitas terbaru
        $aktivitasTerbaru = [
            [
                'type' => 'umkm_baru',
                'message' => 'UMKM baru "Warung Makan Sederhana" mendaftar',
                'time' => '2 menit yang lalu',
                'icon' => 'user-plus',
                'color' => 'blue'
            ],
            [
                'type' => 'verifikasi',
                'message' => 'UMKM "Kerajinan Bambu" telah diverifikasi',
                'time' => '15 menit yang lalu',
                'icon' => 'check',
                'color' => 'green'
            ],
            [
                'type' => 'pelatihan',
                'message' => 'Pelatihan "Digital Marketing" dimulai',
                'time' => '1 jam yang lalu',
                'icon' => 'graduation-cap',
                'color' => 'purple'
            ],
            [
                'type' => 'berita',
                'message' => 'Berita baru "Workshop Pengemasan" dipublikasi',
                'time' => '3 jam yang lalu',
                'icon' => 'newspaper',
                'color' => 'orange'
            ],
            [
                'type' => 'pending',
                'message' => '5 UMKM menunggu verifikasi dokumen',
                'time' => '5 jam yang lalu',
                'icon' => 'exclamation-triangle',
                'color' => 'red'
            ]
        ];

        // Sebaran UMKM per kecamatan
        $sebaranKecamatan = [
            ['nama' => 'Purworejo', 'jumlah' => 156, 'persentase' => 65],
            ['nama' => 'Kutoarjo', 'jumlah' => 142, 'persentase' => 59],
            ['nama' => 'Grabag', 'jumlah' => 98, 'persentase' => 41],
            ['nama' => 'Ngombol', 'jumlah' => 87, 'persentase' => 36],
            ['nama' => 'Lainnya', 'jumlah' => 767, 'persentase' => 100]
        ];

        return view('admin.dashboard', compact(
            'stats',
            'umkmPerBulan',
            'kategoriUmkm',
            'aktivitasTerbaru',
            'sebaranKecamatan'
        ));
    }
}