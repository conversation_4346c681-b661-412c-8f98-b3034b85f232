<?php

namespace App\Models;

use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class User extends Authenticatable
{
    use HasFactory, Notifiable;

    protected $fillable = ['nama', 'email', 'password', 'no_hp', 'role'];

    protected $hidden = ['password', 'remember_token'];

    protected $casts = [
        'email_verified_at' => 'datetime',
    ];

    // Relasi ke profil
    public function profil()
    {
        return $this->hasOne(Profil::class);
    }

    // Relasi ke usaha
    public function usaha()
    {
        return $this->hasOne(Usaha::class);
    }

    // <PERSON>lasi ke pelatihan yang diikuti
    public function pelatihans()
    {
        return $this->belongsToMany(Pelatihan::class)->withTimestamps();
    }
}
