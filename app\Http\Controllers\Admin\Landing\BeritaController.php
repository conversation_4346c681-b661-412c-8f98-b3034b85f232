<?php

namespace App\Http\Controllers\Admin\Landing;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class BeritaController extends Controller
{
    public function index(Request $request)
    {
        // Sample data berita - nanti bisa diganti dengan data dari database
        $berita = collect([
            [
                'id' => 1,
                'judul' => 'Pembukaan Pendaftaran UMKM Tahun 2024',
                'konten' => 'PLUT Purworejo membuka pendaftaran untuk pelaku UMKM...',
                'gambar' => 'https://images.unsplash.com/photo-1504711434969-e33886168f5c?w=600&h=400&fit=crop',
                'kategori' => 'Pengumuman',
                'penulis' => 'Admin PLUT',
                'tanggal' => '2024-07-25',
                'status' => 'Published',
                'views' => 1234,
                'slug' => 'pembukaan-pendaftaran-umkm-2024'
            ],
            [
                'id' => 2,
                'judul' => 'Pelatihan Digital Marketing untuk UMKM',
                'konten' => 'Pelatihan gratis digital marketing akan diselenggarakan...',
                'gambar' => 'https://images.unsplash.com/photo-1552664730-d307ca884978?w=400&h=300&fit=crop',
                'kategori' => 'Pelatihan',
                'penulis' => 'Admin PLUT',
                'tanggal' => '2024-07-20',
                'status' => 'Published',
                'views' => 856,
                'slug' => 'pelatihan-digital-marketing-umkm'
            ],
            [
                'id' => 3,
                'judul' => 'Workshop Pengembangan Produk UMKM',
                'konten' => 'Workshop pengembangan produk untuk meningkatkan...',
                'gambar' => 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=300&fit=crop',
                'kategori' => 'Kegiatan',
                'penulis' => 'Admin PLUT',
                'tanggal' => '2024-07-15',
                'status' => 'Published',
                'views' => 2156,
                'slug' => 'workshop-pengembangan-produk-umkm'
            ],
            [
                'id' => 4,
                'judul' => 'Bantuan Modal Usaha untuk UMKM',
                'konten' => 'Program bantuan modal usaha dengan bunga rendah...',
                'gambar' => 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=300&fit=crop',
                'kategori' => 'Pengumuman',
                'penulis' => 'Admin PLUT',
                'tanggal' => '2024-07-10',
                'status' => 'Draft',
                'views' => 0,
                'slug' => 'bantuan-modal-usaha-umkm'
            ]
        ]);

        // Filter berdasarkan request
        if ($request->filled('search')) {
            $berita = $berita->filter(function ($item) use ($request) {
                return stripos($item['judul'], $request->search) !== false ||
                       stripos($item['konten'], $request->search) !== false;
            });
        }

        if ($request->filled('kategori')) {
            $berita = $berita->where('kategori', $request->kategori);
        }

        if ($request->filled('status')) {
            $berita = $berita->where('status', $request->status);
        }

        if ($request->filled('tanggal')) {
            $berita = $berita->where('tanggal', $request->tanggal);
        }

        // Pagination manual (untuk demo)
        $perPage = 10;
        $currentPage = $request->get('page', 1);
        $total = $berita->count();
        $berita = $berita->slice(($currentPage - 1) * $perPage, $perPage);

        return view('admin.landing.berita', compact('berita', 'total'));
    }

    public function create()
    {
        return view('admin.landing.berita-form', [
            'berita' => null,
            'action' => 'create'
        ]);
    }

    public function store(Request $request)
    {
        $request->validate([
            'judul' => 'required|string|max:255',
            'konten' => 'required|string',
            'kategori' => 'required|string',
            'status' => 'required|in:Published,Draft,Archived',
            'gambar' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        // Logic untuk menyimpan berita
        // Untuk sekarang redirect dengan pesan sukses

        return redirect()->route('admin.landing.berita')
            ->with('success', 'Berita berhasil ditambahkan!');
    }

    public function edit($id)
    {
        // Sample data - nanti ambil dari database
        $berita = [
            'id' => $id,
            'judul' => 'Pembukaan Pendaftaran UMKM Tahun 2024',
            'konten' => 'PLUT Purworejo membuka pendaftaran untuk pelaku UMKM...',
            'kategori' => 'Pengumuman',
            'status' => 'Published',
            'gambar' => 'https://images.unsplash.com/photo-1504711434969-e33886168f5c?w=600&h=400&fit=crop'
        ];

        return view('admin.landing.berita-form', [
            'berita' => $berita,
            'action' => 'edit'
        ]);
    }

    public function update(Request $request, $id)
    {
        $request->validate([
            'judul' => 'required|string|max:255',
            'konten' => 'required|string',
            'kategori' => 'required|string',
            'status' => 'required|in:Published,Draft,Archived',
            'gambar' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        // Logic untuk update berita
        // Untuk sekarang redirect dengan pesan sukses

        return redirect()->route('admin.landing.berita')
            ->with('success', 'Berita berhasil diperbarui!');
    }

    public function destroy($id)
    {
        // Logic untuk hapus berita
        // Untuk sekarang redirect dengan pesan sukses

        return redirect()->route('admin.landing.berita')
            ->with('success', 'Berita berhasil dihapus!');
    }
}