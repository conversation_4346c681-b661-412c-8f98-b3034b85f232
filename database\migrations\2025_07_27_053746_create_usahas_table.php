<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('usahas', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('nama_usaha');
            $table->string('nama_merk')->nullable();
            $table->string('bidang_usaha');
            $table->string('alamat_lengkap');
            $table->string('desa');
            $table->string('kecamatan');
            $table->string('kabupaten')->default('Purworejo');
            $table->string('provinsi')->default('Jawa Tengah');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('usahas');
    }
};
