@extends('layouts.app', ['title' => 'Data UMKM'])

@section('content')
<div class="mb-8">
    <h1 class="text-2xl font-bold text-gray-800">Data UMKM</h1>
    <p class="text-gray-500 mt-2">Daftar seluruh UMKM yang terdaftar di sistem.</p>
</div>
<div class="overflow-x-auto bg-white rounded-xl shadow p-4">
    <table class="min-w-full divide-y divide-gray-200" id="umkm-table">
        <thead class="bg-gray-100">
            <tr>
                <th class="px-4 py-2 text-left text-xs font-semibold text-gray-700">No</th>
                <th class="px-4 py-2 text-left text-xs font-semibold text-gray-700"><PERSON>a <PERSON></th>
                <th class="px-4 py-2 text-left text-xs font-semibold text-gray-700">NIK</th>
                <th class="px-4 py-2 text-left text-xs font-semibold text-gray-700">No HP</th>
                <th class="px-4 py-2 text-left text-xs font-semibold text-gray-700">Email</th>
                <th class="px-4 py-2 text-left text-xs font-semibold text-gray-700">Alamat Pribadi</th>
                <th class="px-4 py-2 text-left text-xs font-semibold text-gray-700">Nama Usaha</th>
                <th class="px-4 py-2 text-left text-xs font-semibold text-gray-700">Merk</th>
                <th class="px-4 py-2 text-left text-xs font-semibold text-gray-700">Bidang Usaha</th>
                <th class="px-4 py-2 text-left text-xs font-semibold text-gray-700">Alamat Usaha</th>
                <th class="px-4 py-2 text-left text-xs font-semibold text-gray-700">Izin yang Dimiliki</th>
                <th class="px-4 py-2 text-left text-xs font-semibold text-gray-700">Aksi</th>
            </tr>
        </thead>
        <tbody class="divide-y divide-gray-100">
            @foreach($umkms as $i => $umkm)
            <tr>
                <td class="px-4 py-2">{{ $i+1 }}</td>
                <td class="px-4 py-2">{{ $umkm->user->nama ?? '-' }}</td>
                <td class="px-4 py-2">{{ $umkm->user->profil->nik ?? '-' }}</td>
                <td class="px-4 py-2">{{ $umkm->user->no_hp ?? '-' }}</td>
                <td class="px-4 py-2">{{ $umkm->user->email ?? '-' }}</td>
                <td class="px-4 py-2">{{ $umkm->user->profil->alamat_lengkap ?? '-' }}</td>
                <td class="px-4 py-2">{{ $umkm->nama_usaha ?? '-' }}</td>
                <td class="px-4 py-2">{{ $umkm->nama_merk ?? '-' }}</td>
                <td class="px-4 py-2">{{ $umkm->bidang_usaha ?? '-' }}</td>
                <td class="px-4 py-2">{{ $umkm->alamat_lengkap ?? '-' }}</td>
                <td class="px-4 py-2">
                    @if($umkm->legalitas->count())
                        @foreach($umkm->legalitas as $legal)
                            <span class="inline-block bg-green-100 text-green-800 text-xs px-2 py-1 rounded mr-1 mb-1">{{ $legal->nama_legalitas }}</span>
                        @endforeach
                    @else
                        <span class="text-gray-400">-</span>
                    @endif
                </td>
                <td class="px-4 py-2">
                    <a href="#" class="text-blue-600 hover:underline text-xs">Detail</a>
                </td>
            </tr>
            @endforeach
        </tbody>
    </table>
    @if(count($umkms) === 0)
        <div class="text-center text-gray-400 py-8">Tidak ada data UMKM.</div>
    @endif
</div>
@endsection

@push('scripts')
<!-- DataTables JS CDN -->
<script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/dataTables.tailwindcss.min.js"></script>
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.6/css/dataTables.tailwindcss.min.css" />
<script>
    document.addEventListener('DOMContentLoaded', function () {
        $('#umkm-table').DataTable();
    });
</script>
@endpush
