<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class PengaturanController extends Controller
{
    public function umum()
    {
        // Pengaturan umum sistem
        $pengaturan = [
            'nama_aplikasi' => 'PLUT Purworejo',
            'deskripsi' => 'Sistem Informasi Pusat Layanan Usaha Terpadu Kabupaten Purworejo',
            'logo' => '/images/logo-plut.png',
            'favicon' => '/images/favicon.ico',
            'timezone' => 'Asia/Jakarta',
            'bahasa' => 'id',
            'maintenance_mode' => false,
            'registrasi_umkm' => true,
            'notifikasi_email' => true,
            'backup_otomatis' => true
        ];

        return view('admin.pengaturan.umum', compact('pengaturan'));
    }

    public function updateUmum(Request $request)
    {
        $request->validate([
            'nama_aplikasi' => 'required|string|max:255',
            'deskripsi' => 'required|string',
            'timezone' => 'required|string',
            'bahasa' => 'required|string',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'favicon' => 'nullable|image|mimes:ico,png|max:1024'
        ]);

        // Logic untuk update pengaturan umum
        return redirect()->route('admin.pengaturan.umum')
            ->with('success', 'Pengaturan umum berhasil diperbarui!');
    }

    public function kontak()
    {
        // Informasi kontak PLUT
        $kontak = [
            'alamat' => 'Jalan Soekarno-Hatta, Unit Rw. IV No.92, Boro Kulon, Kec. Banyuurip, Kabupaten Purworejo, Jawa Tengah 54171',
            'telepon' => '(0275) 321456',
            'whatsapp' => '+62 812-3456-7890',
            'fax' => '(0275) 321457',
            'email_umum' => '<EMAIL>',
            'email_pelatihan' => '<EMAIL>',
            'email_pendaftaran' => '<EMAIL>',
            'website' => 'https://plutpurworejo.go.id',
            'facebook' => 'https://facebook.com/plutpurworejo',
            'instagram' => 'https://instagram.com/plutpurworejo',
            'twitter' => 'https://twitter.com/plutpurworejo',
            'youtube' => 'https://youtube.com/plutpurworejo',
            'jam_operasional' => [
                'senin_kamis' => '08:00 - 16:00',
                'jumat' => '08:00 - 11:30',
                'sabtu' => '08:00 - 12:00',
                'minggu' => 'Tutup'
            ]
        ];

        return view('admin.pengaturan.kontak', compact('kontak'));
    }

    public function updateKontak(Request $request)
    {
        $request->validate([
            'alamat' => 'required|string',
            'telepon' => 'required|string|max:20',
            'whatsapp' => 'required|string|max:20',
            'email_umum' => 'required|email',
            'email_pelatihan' => 'required|email',
            'email_pendaftaran' => 'required|email',
            'website' => 'nullable|url',
            'facebook' => 'nullable|url',
            'instagram' => 'nullable|url',
            'twitter' => 'nullable|url'
        ]);

        // Logic untuk update kontak
        return redirect()->route('admin.pengaturan.kontak')
            ->with('success', 'Informasi kontak berhasil diperbarui!');
    }

    public function backup()
    {
        // Daftar backup yang tersedia
        $backups = [
            [
                'nama' => 'backup_2024_07_25_08_00_00.sql',
                'ukuran' => '15.2 MB',
                'tanggal' => '2024-07-25 08:00:00',
                'tipe' => 'Otomatis'
            ],
            [
                'nama' => 'backup_2024_07_24_08_00_00.sql',
                'ukuran' => '14.8 MB',
                'tanggal' => '2024-07-24 08:00:00',
                'tipe' => 'Otomatis'
            ],
            [
                'nama' => 'backup_manual_2024_07_23_15_30_00.sql',
                'ukuran' => '14.5 MB',
                'tanggal' => '2024-07-23 15:30:00',
                'tipe' => 'Manual'
            ]
        ];

        $pengaturanBackup = [
            'otomatis' => true,
            'jadwal' => 'Harian pada 08:00',
            'simpan_selama' => '30 hari',
            'lokasi' => '/storage/backups/'
        ];

        return view('admin.pengaturan.backup', compact('backups', 'pengaturanBackup'));
    }

    public function createBackup()
    {
        // Logic untuk membuat backup manual
        return redirect()->route('admin.pengaturan.backup')
            ->with('success', 'Backup berhasil dibuat!');
    }
}