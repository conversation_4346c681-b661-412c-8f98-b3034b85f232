<?php

namespace App\Http\Controllers\Admin\Landing;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class BerandaController extends Controller
{
    public function index()
    {
        // Data hero section
        $heroData = [
            'judul_utama' => 'Sistem Informasi PLUT',
            'sub_judul' => 'Kabupaten Purworejo',
            'deskripsi' => 'Menyediakan informasi publik tentang layanan PLUT, mengelola data pelaku UMKM secara mandiri dan terstruktur, serta menyediakan sistem pelatihan berbasis online untuk pengembangan UMKM di Kabupaten Purworejo.'
        ];

        // Data statistik
        $statistik = [
            'umkm_terdaftar' => 1250,
            'pelatihan_tersedia' => 85,
            'peserta_pelatihan' => 2400,
            'tingkat_kepuasan' => 95
        ];

        // Berita unggulan
        $beritaUnggulan = [
            [
                'id' => 1,
                'judul' => 'Pembukaan Pendaftaran UMKM Tahun 2024',
                'deskripsi' => 'PLUT Purworejo membuka pendaftaran...',
                'gambar' => 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400&h=300&fit=crop',
                'tanggal' => '25 Juli 2024',
                'status' => 'aktif'
            ],
            [
                'id' => 2,
                'judul' => 'Pelatihan Digital Marketing untuk UMKM',
                'deskripsi' => 'Pelatihan gratis digital marketing...',
                'gambar' => 'https://images.unsplash.com/photo-1552664730-d307ca884978?w=400&h=300&fit=crop',
                'tanggal' => '20 Juli 2024',
                'status' => 'aktif'
            ],
            [
                'id' => 3,
                'judul' => 'Workshop Pengembangan Produk UMKM',
                'deskripsi' => 'Workshop pengembangan produk...',
                'gambar' => 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=300&fit=crop',
                'tanggal' => '15 Juli 2024',
                'status' => 'aktif'
            ]
        ];

        // Data CTA
        $ctaData = [
            'judul' => 'Siap Bergabung dengan PLUT Purworejo?',
            'deskripsi' => 'Daftarkan UMKM Anda sekarang dan nikmati berbagai layanan dan fasilitas yang tersedia',
            'warna_background' => 'plut-primary'
        ];

        return view('admin.landing.beranda', compact(
            'heroData',
            'statistik',
            'beritaUnggulan',
            'ctaData'
        ));
    }

    public function update(Request $request)
    {
        $request->validate([
            'judul_utama' => 'required|string|max:255',
            'sub_judul' => 'required|string|max:255',
            'deskripsi' => 'required|string',
            'umkm_terdaftar' => 'required|integer',
            'pelatihan_tersedia' => 'required|integer',
            'peserta_pelatihan' => 'required|integer',
            'tingkat_kepuasan' => 'required|integer|min:0|max:100',
            'cta_judul' => 'required|string|max:255',
            'cta_deskripsi' => 'required|string',
            'cta_warna' => 'required|string'
        ]);

        // Di sini nanti bisa disimpan ke database atau file konfigurasi
        // Untuk sekarang kita redirect dengan pesan sukses

        return redirect()->route('admin.landing.beranda')
            ->with('success', 'Pengaturan beranda berhasil diperbarui!');
    }
}