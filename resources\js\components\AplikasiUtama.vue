<template>
  <!-- Template utama aplikasi PLUT -->
  <div id="aplikasi-plut">
    <!-- Header/Navbar -->
    <header class="bg-white shadow-lg sticky top-0 z-50">
      <!-- bg-white: background putih, shadow-lg: bayangan besar, sticky: menempel di atas, top-0: posisi atas 0, z-50: layer tinggi -->
      <nav class="container mx-auto px-4 py-3">
        <!-- container: lebar maksimal dengan margin otomatis, mx-auto: margin horizontal otomatis (tengah), px-4: padding horizontal 4, py-3: padding vertikal 3 -->
        <div class="flex justify-between items-center">
          <!-- flex: display flex, justify-between: space antara elemen, items-center: align vertikal tengah -->
          
          <!-- Logo dan Nama Sistem -->
          <div class="flex items-center space-x-3">
            <!-- space-x-3: jarak horizontal antar elemen 3 -->
            <img src="/images/logo-plut.png" alt="Logo PLUT" class="h-10 w-10">
            <!-- h-10: tinggi 10, w-10: lebar 10 -->
            <div>
              <h1 class="text-xl font-bold text-plut-dark">PLUT Purworejo</h1>
              <!-- text-xl: ukuran teks extra large, font-bold: teks tebal, text-plut-dark: warna teks kustom -->
              <p class="text-sm text-gray-600">Sistem Informasi PLUT</p>
              <!-- text-sm: ukuran teks kecil, text-gray-600: warna abu-abu -->
            </div>
          </div>

          <!-- Menu Navigasi Desktop -->
          <div class="hidden md:flex space-x-6">
            <!-- hidden: tersembunyi, md:flex: tampil flex di layar medium ke atas, space-x-6: jarak horizontal 6 -->
            <a 
              v-for="menu in daftarMenu" 
              :key="menu.nama"
              href="#" 
              @click="gantiHalaman(menu.halaman)"
              class="text-gray-700 hover:text-plut-primary transition duration-300 font-medium"
              :class="{ 'text-plut-primary border-b-2 border-plut-primary': halamanAktif === menu.halaman }"
            >
              <!-- text-gray-700: warna abu-abu gelap, hover:text-plut-primary: warna saat hover, transition: animasi transisi, duration-300: durasi 300ms -->
              {{ menu.nama }}
            </a>
          </div>

          <!-- Tombol Login/Register -->
          <div class="hidden md:flex space-x-3">
            <button 
              @click="gantiHalaman('login')"
              class="px-4 py-2 text-plut-primary border border-plut-primary rounded hover:bg-plut-primary hover:text-white transition duration-300"
            >
              <!-- px-4: padding horizontal 4, py-2: padding vertikal 2, border: border, rounded: sudut melengkung -->
              Masuk
            </button>
            <button 
              @click="gantiHalaman('registrasi')"
              class="tombol-utama"
            >
              Daftar
            </button>
          </div>

          <!-- Tombol Menu Mobile -->
          <button 
            @click="toggleMenuMobile"
            class="md:hidden p-2"
          >
            <!-- md:hidden: tersembunyi di layar medium ke atas, p-2: padding 2 -->
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <!-- w-6: lebar 6, h-6: tinggi 6 -->
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
            </svg>
          </button>
        </div>

        <!-- Menu Mobile -->
        <div v-if="menuMobileTerbuka" class="md:hidden mt-4 pb-4">
          <!-- v-if: kondisional rendering, mt-4: margin top 4, pb-4: padding bottom 4 -->
          <div class="flex flex-col space-y-3">
            <!-- flex-col: flex direction column, space-y-3: jarak vertikal 3 -->
            <a 
              v-for="menu in daftarMenu" 
              :key="menu.nama"
              href="#" 
              @click="gantiHalaman(menu.halaman)"
              class="text-gray-700 hover:text-plut-primary transition duration-300 py-2"
            >
              {{ menu.nama }}
            </a>
            <div class="flex space-x-3 pt-3 border-t">
              <!-- pt-3: padding top 3, border-t: border atas -->
              <button 
                @click="gantiHalaman('login')"
                class="flex-1 px-4 py-2 text-plut-primary border border-plut-primary rounded hover:bg-plut-primary hover:text-white transition duration-300"
              >
                <!-- flex-1: flex grow 1 -->
                Masuk
              </button>
              <button 
                @click="gantiHalaman('registrasi')"
                class="flex-1 tombol-utama"
              >
                Daftar
              </button>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <!-- Konten Utama -->
    <main class="min-h-screen bg-gray-50">
      <!-- min-h-screen: tinggi minimal setinggi layar, bg-gray-50: background abu-abu muda -->
      
      <!-- Halaman Beranda -->
      <div v-if="halamanAktif === 'beranda'">
        <!-- Hero Section -->
        <section class="bg-gradient-to-r from-plut-primary to-plut-secondary text-white py-20">
          <!-- bg-gradient-to-r: gradient ke kanan, from-plut-primary: warna awal, to-plut-secondary: warna akhir, py-20: padding vertikal 20 -->
          <div class="container mx-auto px-4 text-center">
            <h1 class="text-4xl md:text-6xl font-bold mb-6">
              <!-- text-4xl: ukuran teks 4xl, md:text-6xl: ukuran 6xl di layar medium, mb-6: margin bottom 6 -->
              Sistem Informasi PLUT
            </h1>
            <h2 class="text-2xl md:text-3xl mb-8">Kabupaten Purworejo</h2>
            <p class="text-lg md:text-xl mb-8 max-w-3xl mx-auto">
              <!-- max-w-3xl: lebar maksimal 3xl -->
              Menyediakan informasi publik tentang layanan PLUT, mengelola data pelaku UMKM secara mandiri dan terstruktur
            </p>
            <button class="bg-white text-plut-primary px-8 py-3 rounded-lg font-bold hover:bg-gray-100 transition duration-300">
              <!-- px-8: padding horizontal 8, py-3: padding vertikal 3, rounded-lg: sudut melengkung besar -->
              Mulai Sekarang
            </button>
          </div>
        </section>

        <!-- Fitur Utama -->
        <section class="py-16">
          <div class="container mx-auto px-4">
            <h2 class="judul-halaman text-center">Fitur Utama Sistem</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              <!-- grid: display grid, grid-cols-1: 1 kolom, md:grid-cols-2: 2 kolom di medium, lg:grid-cols-3: 3 kolom di large, gap-8: jarak 8 -->
              <div v-for="fitur in daftarFitur" :key="fitur.judul" class="kartu-konten text-center">
                <div class="text-4xl mb-4">{{ fitur.ikon }}</div>
                <h3 class="text-xl font-bold mb-3 text-plut-dark">{{ fitur.judul }}</h3>
                <p class="teks-deskripsi">{{ fitur.deskripsi }}</p>
              </div>
            </div>
          </div>
        </section>
      </div>

      <!-- Halaman Profil PLUT -->
      <div v-else-if="halamanAktif === 'profil'">
        <div class="container mx-auto px-4 py-16">
          <h1 class="judul-halaman">Profil PLUT Kabupaten Purworejo</h1>
          <div class="kartu-konten">
            <p class="teks-deskripsi mb-4">
              Pusat Layanan Usaha Terpadu (PLUT) Kabupaten Purworejo adalah...
            </p>
            <p class="teks-deskripsi">
              [Konten profil akan ditambahkan di sini]
            </p>
          </div>
        </div>
      </div>

      <!-- Halaman Berita -->
      <div v-else-if="halamanAktif === 'berita'">
        <div class="container mx-auto px-4 py-16">
          <h1 class="judul-halaman">Berita Terkini</h1>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div v-for="berita in daftarBerita" :key="berita.id" class="kartu-konten">
              <img :src="berita.gambar" :alt="berita.judul" class="w-full h-48 object-cover rounded-lg mb-4">
              <!-- w-full: lebar penuh, h-48: tinggi 48, object-cover: cover gambar, rounded-lg: sudut melengkung -->
              <h3 class="text-lg font-bold mb-2">{{ berita.judul }}</h3>
              <p class="teks-deskripsi text-sm mb-3">{{ berita.ringkasan }}</p>
              <p class="text-xs text-gray-500">{{ berita.tanggal }}</p>
              <!-- text-xs: ukuran teks extra small, text-gray-500: warna abu-abu -->
            </div>
          </div>
        </div>
      </div>

      <!-- Halaman lainnya akan ditambahkan dengan pola yang sama -->
      <div v-else>
        <div class="container mx-auto px-4 py-16">
          <h1 class="judul-halaman">{{ judulHalaman }}</h1>
          <div class="kartu-konten">
            <p class="teks-deskripsi">
              Halaman {{ halamanAktif }} sedang dalam pengembangan.
            </p>
          </div>
        </div>
      </div>
    </main>

    <!-- Footer -->
    <footer class="bg-plut-dark text-white py-12">
      <div class="container mx-auto px-4">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div>
            <h3 class="text-lg font-bold mb-4">PLUT Purworejo</h3>
            <p class="text-gray-300 mb-4">
              <!-- text-gray-300: warna abu-abu muda -->
              Sistem Informasi Pusat Layanan Usaha Terpadu Kabupaten Purworejo
            </p>
          </div>
          <div>
            <h3 class="text-lg font-bold mb-4">Menu Cepat</h3>
            <ul class="space-y-2">
              <!-- space-y-2: jarak vertikal 2 -->
              <li v-for="menu in daftarMenu" :key="menu.nama">
                <a href="#" @click="gantiHalaman(menu.halaman)" class="text-gray-300 hover:text-white transition duration-300">
                  {{ menu.nama }}
                </a>
              </li>
            </ul>
          </div>
          <div>
            <h3 class="text-lg font-bold mb-4">Kontak</h3>
            <p class="text-gray-300">
              Alamat: [Alamat PLUT Purworejo]<br>
              Telepon: [Nomor Telepon]<br>
              Email: [Email Kontak]
            </p>
          </div>
        </div>
        <div class="border-t border-gray-600 mt-8 pt-8 text-center">
          <!-- border-t: border atas, border-gray-600: warna border abu-abu, mt-8: margin top 8, pt-8: padding top 8 -->
          <p class="text-gray-300">
            &copy; 2024 PLUT Kabupaten Purworejo. Semua hak dilindungi.
          </p>
        </div>
      </div>
    </footer>
  </div>
</template>

<script>
export default {
  name: 'AplikasiUtama',
  data() {
    // Data reaktif untuk aplikasi
    return {
      // Halaman yang sedang aktif
      halamanAktif: 'beranda',
      
      // Status menu mobile (terbuka/tertutup)
      menuMobileTerbuka: false,
      
      // Daftar menu navigasi
      daftarMenu: [
        { nama: 'Beranda', halaman: 'beranda' },
        { nama: 'Profil PLUT', halaman: 'profil' },
        { nama: 'Berita', halaman: 'berita' },
        { nama: 'Pelatihan', halaman: 'pelatihan' },
        { nama: 'Galeri', halaman: 'galeri' },
        { nama: 'Peta Lokasi', halaman: 'peta' },
        { nama: 'Kontak Kami', halaman: 'kontak' }
      ],
      
      // Daftar fitur utama sistem
      daftarFitur: [
        {
          ikon: '🏢',
          judul: 'Informasi PLUT',
          deskripsi: 'Menyediakan informasi lengkap tentang layanan dan fasilitas PLUT'
        },
        {
          ikon: '👥',
          judul: 'Registrasi UMKM',
          deskripsi: 'Pendaftaran pelaku UMKM secara mandiri dan terstruktur'
        },
        {
          ikon: '📚',
          judul: 'Pelatihan Online',
          deskripsi: 'Sistem pelatihan berbasis online untuk pengembangan UMKM'
        },
        {
          ikon: '📊',
          judul: 'Statistik Data',
          deskripsi: 'Menyediakan statistik dan data UMKM untuk kebutuhan dinas'
        },
        {
          ikon: '🗺️',
          judul: 'Peta Lokasi',
          deskripsi: 'Pemetaan lokasi UMKM dan fasilitas PLUT di Purworejo'
        },
        {
          ikon: '📞',
          judul: 'Layanan Kontak',
          deskripsi: 'Kemudahan komunikasi dengan tim PLUT Purworejo'
        }
      ],
      
      // Contoh data berita
      daftarBerita: [
        {
          id: 1,
          judul: 'Pembukaan Pendaftaran UMKM Tahun 2024',
          ringkasan: 'PLUT Purworejo membuka pendaftaran untuk pelaku UMKM...',
          gambar: '/images/berita1.jpg',
          tanggal: '25 Juli 2024'
        },
        {
          id: 2,
          judul: 'Pelatihan Digital Marketing untuk UMKM',
          ringkasan: 'Pelatihan gratis digital marketing akan diselenggarakan...',
          gambar: '/images/berita2.jpg',
          tanggal: '20 Juli 2024'
        },
        {
          id: 3,
          judul: 'Workshop Pengembangan Produk UMKM',
          ringkasan: 'Workshop pengembangan produk untuk meningkatkan kualitas...',
          gambar: '/images/berita3.jpg',
          tanggal: '15 Juli 2024'
        }
      ]
    }
  },
  
  computed: {
    // Computed property untuk judul halaman dinamis
    judulHalaman() {
      const menu = this.daftarMenu.find(m => m.halaman === this.halamanAktif);
      return menu ? menu.nama : 'Halaman';
    }
  },
  
  methods: {
    // Method untuk mengganti halaman
    gantiHalaman(halaman) {
      this.halamanAktif = halaman;
      this.menuMobileTerbuka = false; // Tutup menu mobile setelah navigasi
    },
    
    // Method untuk toggle menu mobile
    toggleMenuMobile() {
      this.menuMobileTerbuka = !this.menuMobileTerbuka;
    }
  }
}
</script>
