<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>@yield('title', 'Dashboard Admin') - PLUT Purworejo</title>
    
    <!-- Tailwind CSS -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    
    <style[x-cloak] { display: none !important; }
        
        /* Ensure proper layout alignment */
        .admin-layout {
            min-height: 100vh;
        }
        
        /* Fix sidebar positioning and alignment */
        @media (min-width: 1024px) {
            .sidebar-fixed {
                position: fixed !important;
                top: 0;
                left: 0;
                height: 100vh;
                z-index: 40;
            }
            
            .content-with-sidebar {
                margin-left: 16rem; /* 256px = w-64 */
            }
        }
        
        /* Ensure content doesn't overlap */
        .admin-content {
            width: 100%;
            overflow-x: auto;
        }
        
        /* Fix navigation alignment */
        .nav-item {
            display: flex;
            align-items: center;
            width: 100%;
        }
        
        /* Mobile responsive */
        @media (max-width: 1023px) {
            .content-with-sidebar {
                margin-left: 0 !important;
            }
        }
    </style>
</head>
<body class="bg-gray-100 admin-layout" x-data="{ sidebarOpen: false }">
    <!-- Sidebar -->
    <div class="fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0" 
         :class="{ '-translate-x-full': !sidebarOpen, 'translate-x-0': sidebarOpen }">
        
        <!-- Logo -->
        <div class="flex items-center justify-center h-16 bg-plut-primary">
            <h1 class="text-white text-xl font-bold">Admin PLUT</h1>
        </div>
        
        <!-- Navigation -->
        <nav class="mt-8">
            <div class="px-4 space-y-2">
                <!-- Dashboard -->
                <a href="{{ route('admin.dashboard') }}" 
                   class="flex items-center px-4 py-3 text-gray-700 rounded-lg hover:bg-plut-primary hover:text-white transition-colors duration-200 {{ request()->routeIs('admin.dashboard') ? 'bg-plut-primary text-white' : '' }}">
                    <i class="fas fa-tachometer-alt w-5 h-5 mr-3"></i>
                    <span>Dashboard</span>
                </a>

                <!-- Manajemen Landing Page -->
                <div x-data="{ open: {{ request()->routeIs('admin.landing.*') ? 'true' : 'false' }} }">
                    <button @click="open = !open" 
                            class="flex items-center justify-between w-full px-4 py-3 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                        <div class="flex items-center">
                            <i class="fas fa-globe w-5 h-5 mr-3"></i>
                            <span>Landing Page</span>
                        </div>
                        <i class="fas fa-chevron-down transform transition-transform duration-200" :class="{ 'rotate-180': open }"></i>
                    </button>
                    <div x-show="open" x-cloak class="ml-8 mt-2 space-y-2">
                        <a href="{{ route('admin.landing.beranda') }}" 
                           class="flex items-center px-4 py-2 text-sm text-gray-600 rounded-lg hover:bg-plut-primary hover:text-white transition-colors duration-200 {{ request()->routeIs('admin.landing.beranda') ? 'bg-plut-primary text-white' : '' }}">
                            <i class="fas fa-home w-4 h-4 mr-2"></i>
                            Beranda
                        </a>
                        <a href="{{ route('admin.landing.profil') }}" 
                           class="flex items-center px-4 py-2 text-sm text-gray-600 rounded-lg hover:bg-plut-primary hover:text-white transition-colors duration-200 {{ request()->routeIs('admin.landing.profil') ? 'bg-plut-primary text-white' : '' }}">
                            <i class="fas fa-building w-4 h-4 mr-2"></i>
                            Profil PLUT
                        </a>
                        <a href="{{ route('admin.landing.berita') }}" 
                           class="flex items-center px-4 py-2 text-sm text-gray-600 rounded-lg hover:bg-plut-primary hover:text-white transition-colors duration-200 {{ request()->routeIs('admin.landing.berita') ? 'bg-plut-primary text-white' : '' }}">
                            <i class="fas fa-newspaper w-4 h-4 mr-2"></i>
                            Berita
                        </a>
                        <a href="{{ route('admin.landing.pelatihan') }}" 
                           class="flex items-center px-4 py-2 text-sm text-gray-600 rounded-lg hover:bg-plut-primary hover:text-white transition-colors duration-200 {{ request()->routeIs('admin.landing.pelatihan') ? 'bg-plut-primary text-white' : '' }}">
                            <i class="fas fa-graduation-cap w-4 h-4 mr-2"></i>
                            Pelatihan
                        </a>
                        <a href="{{ route('admin.landing.galeri') }}" 
                           class="flex items-center px-4 py-2 text-sm text-gray-600 rounded-lg hover:bg-plut-primary hover:text-white transition-colors duration-200 {{ request()->routeIs('admin.landing.galeri') ? 'bg-plut-primary text-white' : '' }}">
                            <i class="fas fa-images w-4 h-4 mr-2"></i>
                            Galeri
                        </a>
                    </div>
                </div>

                <!-- Manajemen UMKM -->
                <div x-data="{ open: {{ request()->routeIs('admin.umkm.*') ? 'true' : 'false' }} }">
                    <button @click="open = !open" 
                            class="flex items-center justify-between w-full px-4 py-3 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                        <div class="flex items-center">
                            <i class="fas fa-store w-5 h-5 mr-3"></i>
                            <span>Manajemen UMKM</span>
                        </div>
                        <i class="fas fa-chevron-down transform transition-transform duration-200" :class="{ 'rotate-180': open }"></i>
                    </button>
                    <div x-show="open" x-cloak class="ml-8 mt-2 space-y-2">
                        <a href="{{ route('admin.umkm.index') }}" 
                           class="flex items-center px-4 py-2 text-sm text-gray-600 rounded-lg hover:bg-plut-primary hover:text-white transition-colors duration-200 {{ request()->routeIs('admin.umkm.index') ? 'bg-plut-primary text-white' : '' }}">
                            <i class="fas fa-list w-4 h-4 mr-2"></i>
                            Daftar UMKM
                        </a>
                        <a href="{{ route('admin.umkm.pendaftaran') }}" 
                           class="flex items-center px-4 py-2 text-sm text-gray-600 rounded-lg hover:bg-plut-primary hover:text-white transition-colors duration-200 {{ request()->routeIs('admin.umkm.pendaftaran') ? 'bg-plut-primary text-white' : '' }}">
                            <i class="fas fa-user-plus w-4 h-4 mr-2"></i>
                            Pendaftaran Baru
                        </a>
                        <a href="{{ route('admin.umkm.kategori') }}" 
                           class="flex items-center px-4 py-2 text-sm text-gray-600 rounded-lg hover:bg-plut-primary hover:text-white transition-colors duration-200 {{ request()->routeIs('admin.umkm.kategori') ? 'bg-plut-primary text-white' : '' }}">
                            <i class="fas fa-tags w-4 h-4 mr-2"></i>
                            Kategori UMKM
                        </a>
                        <a href="{{ route('admin.umkm.verifikasi') }}" 
                           class="flex items-center px-4 py-2 text-sm text-gray-600 rounded-lg hover:bg-plut-primary hover:text-white transition-colors duration-200 {{ request()->routeIs('admin.umkm.verifikasi') ? 'bg-plut-primary text-white' : '' }}">
                            <i class="fas fa-check-circle w-4 h-4 mr-2"></i>
                            Verifikasi
                        </a>
                    </div>
                </div>

                <!-- Manajemen User -->
                <div x-data="{ open: {{ request()->routeIs('admin.users.*') ? 'true' : 'false' }} }">
                    <button @click="open = !open" 
                            class="flex items-center justify-between w-full px-4 py-3 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                        <div class="flex items-center">
                            <i class="fas fa-users w-5 h-5 mr-3"></i>
                            <span>Manajemen User</span>
                        </div>
                        <i class="fas fa-chevron-down transform transition-transform duration-200" :class="{ 'rotate-180': open }"></i>
                    </button>
                    <div x-show="open" x-cloak class="ml-8 mt-2 space-y-2">
                        <a href="{{ route('admin.users.index') }}" 
                           class="flex items-center px-4 py-2 text-sm text-gray-600 rounded-lg hover:bg-plut-primary hover:text-white transition-colors duration-200 {{ request()->routeIs('admin.users.index') ? 'bg-plut-primary text-white' : '' }}">
                            <i class="fas fa-user w-4 h-4 mr-2"></i>
                            Daftar User
                        </a>
                        <a href="{{ route('admin.users.admin') }}" 
                           class="flex items-center px-4 py-2 text-sm text-gray-600 rounded-lg hover:bg-plut-primary hover:text-white transition-colors duration-200 {{ request()->routeIs('admin.users.admin') ? 'bg-plut-primary text-white' : '' }}">
                            <i class="fas fa-user-shield w-4 h-4 mr-2"></i>
                            Admin
                        </a>
                        <a href="{{ route('admin.users.roles') }}" 
                           class="flex items-center px-4 py-2 text-sm text-gray-600 rounded-lg hover:bg-plut-primary hover:text-white transition-colors duration-200 {{ request()->routeIs('admin.users.roles') ? 'bg-plut-primary text-white' : '' }}">
                            <i class="fas fa-key w-4 h-4 mr-2"></i>
                            Role & Permission
                        </a>
                    </div>
                </div>

                <!-- Laporan -->
                <div x-data="{ open: {{ request()->routeIs('admin.laporan.*') ? 'true' : 'false' }} }">
                    <button @click="open = !open" 
                            class="flex items-center justify-between w-full px-4 py-3 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                        <div class="flex items-center">
                            <i class="fas fa-chart-bar w-5 h-5 mr-3"></i>
                            <span>Laporan</span>
                        </div>
                        <i class="fas fa-chevron-down transform transition-transform duration-200" :class="{ 'rotate-180': open }"></i>
                    </button>
                    <div x-show="open" x-cloak class="ml-8 mt-2 space-y-2">
                        <a href="{{ route('admin.laporan.umkm') }}" 
                           class="flex items-center px-4 py-2 text-sm text-gray-600 rounded-lg hover:bg-plut-primary hover:text-white transition-colors duration-200 {{ request()->routeIs('admin.laporan.umkm') ? 'bg-plut-primary text-white' : '' }}">
                            <i class="fas fa-chart-pie w-4 h-4 mr-2"></i>
                            Laporan UMKM
                        </a>
                        <a href="{{ route('admin.laporan.pelatihan') }}" 
                           class="flex items-center px-4 py-2 text-sm text-gray-600 rounded-lg hover:bg-plut-primary hover:text-white transition-colors duration-200 {{ request()->routeIs('admin.laporan.pelatihan') ? 'bg-plut-primary text-white' : '' }}">
                            <i class="fas fa-chart-line w-4 h-4 mr-2"></i>
                            Laporan Pelatihan
                        </a>
                        <a href="{{ route('admin.laporan.statistik') }}" 
                           class="flex items-center px-4 py-2 text-sm text-gray-600 rounded-lg hover:bg-plut-primary hover:text-white transition-colors duration-200 {{ request()->routeIs('admin.laporan.statistik') ? 'bg-plut-primary text-white' : '' }}">
                            <i class="fas fa-chart-area w-4 h-4 mr-2"></i>
                            Statistik
                        </a>
                    </div>
                </div>

                <!-- Pengaturan -->
                <div x-data="{ open: {{ request()->routeIs('admin.pengaturan.*') ? 'true' : 'false' }} }">
                    <button @click="open = !open" 
                            class="flex items-center justify-between w-full px-4 py-3 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                        <div class="flex items-center">
                            <i class="fas fa-cog w-5 h-5 mr-3"></i>
                            <span>Pengaturan</span>
                        </div>
                        <i class="fas fa-chevron-down transform transition-transform duration-200" :class="{ 'rotate-180': open }"></i>
                    </button>
                    <div x-show="open" x-cloak class="ml-8 mt-2 space-y-2">
                        <a href="{{ route('admin.pengaturan.umum') }}" 
                           class="flex items-center px-4 py-2 text-sm text-gray-600 rounded-lg hover:bg-plut-primary hover:text-white transition-colors duration-200 {{ request()->routeIs('admin.pengaturan.umum') ? 'bg-plut-primary text-white' : '' }}">
                            <i class="fas fa-sliders-h w-4 h-4 mr-2"></i>
                            Pengaturan Umum
                        </a>
                        <a href="{{ route('admin.pengaturan.kontak') }}" 
                           class="flex items-center px-4 py-2 text-sm text-gray-600 rounded-lg hover:bg-plut-primary hover:text-white transition-colors duration-200 {{ request()->routeIs('admin.pengaturan.kontak') ? 'bg-plut-primary text-white' : '' }}">
                            <i class="fas fa-address-book w-4 h-4 mr-2"></i>
                            Info Kontak
                        </a>
                        <a href="{{ route('admin.pengaturan.backup') }}" 
                           class="flex items-center px-4 py-2 text-sm text-gray-600 rounded-lg hover:bg-plut-primary hover:text-white transition-colors duration-200 {{ request()->routeIs('admin.pengaturan.backup') ? 'bg-plut-primary text-white' : '' }}">
                            <i class="fas fa-database w-4 h-4 mr-2"></i>
                            Backup Data
                        </a>
                    </div>
                </div>
            </div>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="lg:ml-64">
        <!-- Top Navigation -->
        <header class="bg-white shadow-sm border-b border-gray-200">
            <div class="flex items-center justify-between px-6 py-4">
                <!-- Mobile menu button -->
                <button @click="sidebarOpen = !sidebarOpen" class="lg:hidden text-gray-500 hover:text-gray-700">
                    <i class="fas fa-bars text-xl"></i>
                </button>

                <!-- Page Title -->
                <div class="flex-1 lg:flex-none">
                    <h1 class="text-2xl font-semibold text-gray-900">@yield('page-title', 'Dashboard')</h1>
                </div>

                <!-- Right side -->
                <div class="flex items-center space-x-4">
                    <!-- Notifications -->
                    <div class="relative" x-data="{ open: false }">
                        <button @click="open = !open" class="relative p-2 text-gray-500 hover:text-gray-700">
                            <i class="fas fa-bell text-xl"></i>
                            <span class="absolute top-0 right-0 block h-2 w-2 bg-red-500 rounded-full"></span>
                        </button>
                        <div x-show="open" @click.away="open = false" x-cloak 
                             class="absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
                            <div class="p-4 border-b border-gray-200">
                                <h3 class="text-lg font-semibold">Notifikasi</h3>
                            </div>
                            <div class="max-h-64 overflow-y-auto">
                                <div class="p-4 border-b border-gray-100 hover:bg-gray-50">
                                    <p class="text-sm text-gray-800">UMKM baru mendaftar</p>
                                    <p class="text-xs text-gray-500 mt-1">2 menit yang lalu</p>
                                </div>
                                <div class="p-4 border-b border-gray-100 hover:bg-gray-50">
                                    <p class="text-sm text-gray-800">Pelatihan baru ditambahkan</p>
                                    <p class="text-xs text-gray-500 mt-1">1 jam yang lalu</p>
                                </div>
                            </div>
                            <div class="p-4">
                                <a href="#" class="text-sm text-plut-primary hover:underline">Lihat semua notifikasi</a>
                            </div>
                        </div>
                    </div>

                    <!-- User Menu -->
                    <div class="relative" x-data="{ open: false }">
                        <button @click="open = !open" class="flex items-center space-x-2 text-gray-700 hover:text-gray-900">
                            <img src="https://ui-avatars.com/api/?name=Admin&background=3b82f6&color=fff" 
                                 alt="Admin" class="w-8 h-8 rounded-full">
                            <span class="hidden md:block">Admin</span>
                            <i class="fas fa-chevron-down text-sm"></i>
                        </button>
                        <div x-show="open" @click.away="open = false" x-cloak 
                             class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
                            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-user mr-2"></i>Profil
                            </a>
                            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-cog mr-2"></i>Pengaturan
                            </a>
                            <hr class="my-1">
                            <a href="{{ route('beranda') }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-external-link-alt mr-2"></i>Lihat Website
                            </a>
                            <form method="POST" action="{{ route('logout') }}">
                                @csrf
                                <button type="submit" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <i class="fas fa-sign-out-alt mr-2"></i>Logout
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Page Content -->
        <main class="p-6">
            @if(session('success'))
                <div class="mb-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative" role="alert">
                    <span class="block sm:inline">{{ session('success') }}</span>
                </div>
            @endif

            @if(session('error'))
                <div class="mb-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                    <span class="block sm:inline">{{ session('error') }}</span>
                </div>
            @endif

            @yield('content')
        </main>
    </div>

    <!-- Mobile Sidebar Overlay -->
    <div x-show="sidebarOpen" @click="sidebarOpen = false" 
         class="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden" x-cloak></div>
</body>
</html>