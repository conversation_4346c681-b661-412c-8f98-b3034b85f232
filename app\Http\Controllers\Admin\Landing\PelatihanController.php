<?php

namespace App\Http\Controllers\Admin\Landing;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class PelatihanController extends Controller
{
    public function index(Request $request)
    {
        // Sample data pelatihan - nanti bisa diganti dengan data dari database
        $pelatihan = collect([
            [
                'id' => 1,
                'nama' => 'Pelatihan Digital Marketing untuk UMKM',
                'deskripsi' => 'Strategi pemasaran digital yang efektif',
                'gambar' => 'https://images.unsplash.com/photo-1552664730-d307ca884978?w=100&h=100&fit=crop',
                'kategori' => 'Digital Marketing',
                'tipe' => 'Online',
                'tanggal' => '2024-08-20',
                'durasi' => '3 Hari (24 Jam)',
                'peserta_terdaftar' => 45,
                'kapasitas' => 50,
                'status' => 'Akan Datang',
                'instruktur' => 'Dr<PERSON>',
                'rating' => 4.8
            ],
            [
                'id' => 2,
                'nama' => 'Manajemen <PERSON>uangan UMKM',
                'deskripsi' => 'Kelola keuangan usaha dengan baik',
                'gambar' => 'https://images.unsplash.com/photo-1554224155-6726b3ff858f?w=100&h=100&fit=crop',
                'kategori' => 'Keuangan',
                'tipe' => 'Offline',
                'tanggal' => '2024-08-25',
                'durasi' => '2 Hari (16 Jam)',
                'peserta_terdaftar' => 30,
                'kapasitas' => 30,
                'status' => 'Sedang Berjalan',
                'instruktur' => 'Siti Nurhaliza, S.E.',
                'rating' => 4.9
            ],
            [
                'id' => 3,
                'nama' => 'Teknik Pengemasan Produk Modern',
                'deskripsi' => 'Pengemasan yang menarik dan aman',
                'gambar' => 'https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=100&h=100&fit=crop',
                'kategori' => 'Produksi',
                'tipe' => 'Hybrid',
                'tanggal' => '2024-09-05',
                'durasi' => '1 Hari (8 Jam)',
                'peserta_terdaftar' => 15,
                'kapasitas' => 25,
                'status' => 'Akan Datang',
                'instruktur' => 'Budi Santoso',
                'rating' => 4.7
            ]
        ]);

        // Filter berdasarkan request
        if ($request->filled('search')) {
            $pelatihan = $pelatihan->filter(function ($item) use ($request) {
                return stripos($item['nama'], $request->search) !== false ||
                       stripos($item['deskripsi'], $request->search) !== false;
            });
        }

        if ($request->filled('kategori')) {
            $pelatihan = $pelatihan->where('kategori', $request->kategori);
        }

        if ($request->filled('status')) {
            $pelatihan = $pelatihan->where('status', $request->status);
        }

        if ($request->filled('tipe')) {
            $pelatihan = $pelatihan->where('tipe', $request->tipe);
        }

        // Statistik pelatihan
        $stats = [
            'total_pelatihan' => 85,
            'sedang_berjalan' => 12,
            'total_peserta' => 2400,
            'rating_rata_rata' => 4.8
        ];

        // Kategori populer
        $kategoriPopuler = [
            ['nama' => 'Digital Marketing', 'jumlah' => 25, 'persentase' => 85],
            ['nama' => 'Keuangan', 'jumlah' => 20, 'persentase' => 70],
            ['nama' => 'Produksi', 'jumlah' => 18, 'persentase' => 60],
            ['nama' => 'Manajemen', 'jumlah' => 15, 'persentase' => 45]
        ];

        // Feedback terbaru
        $feedbackTerbaru = [
            [
                'pelatihan' => 'Digital Marketing UMKM',
                'rating' => 5,
                'komentar' => 'Pelatihan sangat bermanfaat dan mudah dipahami',
                'peserta' => 'Siti Aminah',
                'waktu' => '2 jam lalu'
            ],
            [
                'pelatihan' => 'Manajemen Keuangan',
                'rating' => 4,
                'komentar' => 'Materi lengkap, instruktur berpengalaman',
                'peserta' => 'Budi Santoso',
                'waktu' => '5 jam lalu'
            ],
            [
                'pelatihan' => 'Teknik Pengemasan',
                'rating' => 5,
                'komentar' => 'Praktis dan langsung bisa diterapkan',
                'peserta' => 'Rina Wati',
                'waktu' => '1 hari lalu'
            ]
        ];

        return view('admin.landing.pelatihan', compact(
            'pelatihan',
            'stats',
            'kategoriPopuler',
            'feedbackTerbaru'
        ));
    }

    public function create()
    {
        return view('admin.landing.pelatihan-form', [
            'pelatihan' => null,
            'action' => 'create'
        ]);
    }

    public function store(Request $request)
    {
        $request->validate([
            'nama' => 'required|string|max:255',
            'deskripsi' => 'required|string',
            'kategori' => 'required|string',
            'tipe' => 'required|in:Online,Offline,Hybrid',
            'tanggal' => 'required|date',
            'durasi' => 'required|string',
            'kapasitas' => 'required|integer|min:1',
            'instruktur' => 'required|string|max:255',
            'gambar' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        // Logic untuk menyimpan pelatihan
        // Untuk sekarang redirect dengan pesan sukses

        return redirect()->route('admin.landing.pelatihan')
            ->with('success', 'Pelatihan berhasil ditambahkan!');
    }

    public function edit($id)
    {
        // Sample data - nanti ambil dari database
        $pelatihan = [
            'id' => $id,
            'nama' => 'Pelatihan Digital Marketing untuk UMKM',
            'deskripsi' => 'Strategi pemasaran digital yang efektif',
            'kategori' => 'Digital Marketing',
            'tipe' => 'Online',
            'tanggal' => '2024-08-20',
            'durasi' => '3 Hari (24 Jam)',
            'kapasitas' => 50,
            'instruktur' => 'Dr. Ahmad Wijaya',
            'gambar' => 'https://images.unsplash.com/photo-1552664730-d307ca884978?w=400&h=300&fit=crop'
        ];

        return view('admin.landing.pelatihan-form', [
            'pelatihan' => $pelatihan,
            'action' => 'edit'
        ]);
    }

    public function update(Request $request, $id)
    {
        $request->validate([
            'nama' => 'required|string|max:255',
            'deskripsi' => 'required|string',
            'kategori' => 'required|string',
            'tipe' => 'required|in:Online,Offline,Hybrid',
            'tanggal' => 'required|date',
            'durasi' => 'required|string',
            'kapasitas' => 'required|integer|min:1',
            'instruktur' => 'required|string|max:255',
            'gambar' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        // Logic untuk update pelatihan
        // Untuk sekarang redirect dengan pesan sukses

        return redirect()->route('admin.landing.pelatihan')
            ->with('success', 'Pelatihan berhasil diperbarui!');
    }

    public function destroy($id)
    {
        // Logic untuk hapus pelatihan
        // Untuk sekarang redirect dengan pesan sukses

        return redirect()->route('admin.landing.pelatihan')
            ->with('success', 'Pelatihan berhasil dihapus!');
    }
}