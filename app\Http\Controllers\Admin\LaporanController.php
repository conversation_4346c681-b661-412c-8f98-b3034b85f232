<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class LaporanController extends Controller
{
    public function umkm()
    {
        // Data laporan UMKM
        $stats = [
            'total_umkm' => 1250,
            'umkm_aktif' => 1156,
            'umkm_pending' => 24,
            'umkm_ditolak' => 70
        ];

        // Data per kategori
        $perKategori = [
            ['kategori' => '<PERSON><PERSON><PERSON> & Minuman', 'jumlah' => 450, 'persentase' => 36],
            ['kategori' => 'Kerajinan', 'jumlah' => 320, 'persentase' => 26],
            ['kategori' => 'Fashion', 'jumlah' => 280, 'persentase' => 22],
            ['kategori' => 'Jasa', 'jumlah' => 150, 'persentase' => 12],
            ['kategori' => 'Teknologi', 'jumlah' => 50, 'persentase' => 4]
        ];

        // Data per kecamatan
        $perKecamatan = [
            ['kecamatan' => 'Purworejo', 'jumlah' => 156],
            ['kecamatan' => 'Kutoarjo', 'jumlah' => 142],
            ['kecamatan' => 'Grabag', 'jumlah' => 98],
            ['kecamatan' => 'Ngombol', 'jumlah' => 87],
            ['kecamatan' => 'Lainnya', 'jumlah' => 767]
        ];

        // Trend pendaftaran per bulan
        $trendPendaftaran = [
            'labels' => ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],
            'data' => [65, 78, 90, 81, 95, 105, 110]
        ];

        return view('admin.laporan.umkm', compact(
            'stats',
            'perKategori',
            'perKecamatan',
            'trendPendaftaran'
        ));
    }

    public function pelatihan()
    {
        // Data laporan pelatihan
        $stats = [
            'total_pelatihan' => 85,
            'pelatihan_aktif' => 12,
            'total_peserta' => 2400,
            'tingkat_kelulusan' => 92
        ];

        // Data per kategori pelatihan
        $perKategori = [
            ['kategori' => 'Digital Marketing', 'jumlah' => 25, 'peserta' => 750],
            ['kategori' => 'Keuangan', 'jumlah' => 20, 'peserta' => 600],
            ['kategori' => 'Produksi', 'jumlah' => 18, 'peserta' => 540],
            ['kategori' => 'Manajemen', 'jumlah' => 15, 'peserta' => 450],
            ['kategori' => 'Lainnya', 'jumlah' => 7, 'peserta' => 210]
        ];

        // Rating pelatihan
        $ratingPelatihan = [
            ['nama' => 'Digital Marketing UMKM', 'rating' => 4.8, 'peserta' => 150],
            ['nama' => 'Manajemen Keuangan', 'rating' => 4.9, 'peserta' => 120],
            ['nama' => 'Teknik Pengemasan', 'rating' => 4.7, 'peserta' => 100],
            ['nama' => 'Branding Produk', 'rating' => 4.6, 'peserta' => 90],
            ['nama' => 'E-commerce', 'rating' => 4.8, 'peserta' => 85]
        ];

        return view('admin.laporan.pelatihan', compact(
            'stats',
            'perKategori',
            'ratingPelatihan'
        ));
    }

    public function statistik()
    {
        // Data statistik umum
        $overallStats = [
            'total_users' => 1350,
            'total_umkm' => 1250,
            'total_pelatihan' => 85,
            'total_berita' => 45
        ];

        // Aktivitas bulanan
        $aktivitasBulanan = [
            'labels' => ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],
            'umkm' => [65, 78, 90, 81, 95, 105, 110],
            'pelatihan' => [5, 7, 8, 6, 9, 12, 10],
            'peserta' => [150, 210, 280, 240, 320, 380, 350]
        ];

        // Top performing metrics
        $topMetrics = [
            'umkm_terpopuler' => [
                ['nama' => 'Warung Makan Sederhana', 'views' => 1250],
                ['nama' => 'Kerajinan Bambu Indah', 'views' => 980],
                ['nama' => 'Fashion Store Modern', 'views' => 850]
            ],
            'pelatihan_terpopuler' => [
                ['nama' => 'Digital Marketing', 'peserta' => 150],
                ['nama' => 'Manajemen Keuangan', 'peserta' => 120],
                ['nama' => 'Teknik Pengemasan', 'peserta' => 100]
            ],
            'berita_terpopuler' => [
                ['judul' => 'Pembukaan Pendaftaran UMKM 2024', 'views' => 2156],
                ['judul' => 'Workshop Pengembangan Produk', 'views' => 1890],
                ['judul' => 'Pelatihan Digital Marketing', 'views' => 1650]
            ]
        ];

        return view('admin.laporan.statistik', compact(
            'overallStats',
            'aktivitasBulanan',
            'topMetrics'
        ));
    }
}