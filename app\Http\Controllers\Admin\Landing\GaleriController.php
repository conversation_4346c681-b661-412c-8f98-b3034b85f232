<?php

namespace App\Http\Controllers\Admin\Landing;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class GaleriController extends Controller
{
    public function index()
    {
        // Sample data galeri - nanti bisa diganti dengan data dari database
        $galeri = collect([
            [
                'id' => 1,
                'judul' => 'Pelatihan Digital Marketing',
                'deskripsi' => 'Kegiatan pelatihan digital marketing untuk UMKM',
                'gambar' => 'https://images.unsplash.com/photo-1552664730-d307ca884978?w=400&h=400&fit=crop',
                'kategori' => 'pelatihan',
                'tanggal' => '2024-07-20',
                'views' => 150
            ],
            [
                'id' => 2,
                'judul' => 'Workshop Pengemasan Produk',
                'deskripsi' => 'Workshop teknik pengemasan produk modern',
                'gambar' => 'https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=400&h=400&fit=crop',
                'kategori' => 'workshop',
                'tanggal' => '2024-07-15',
                'views' => 120
            ],
            [
                'id' => 3,
                'judul' => 'Bazar Produk UMKM',
                'deskripsi' => 'Pameran dan bazar produk UMKM Purworejo',
                'gambar' => 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400&h=400&fit=crop',
                'kategori' => 'event',
                'tanggal' => '2024-07-10',
                'views' => 200
            ],
            [
                'id' => 4,
                'judul' => 'Kunjungan Dinas Koperasi',
                'deskripsi' => 'Kunjungan kerja dari Dinas Koperasi Provinsi',
                'gambar' => 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop',
                'kategori' => 'kunjungan',
                'tanggal' => '2024-07-05',
                'views' => 80
            ],
            [
                'id' => 5,
                'judul' => 'Pelatihan Manajemen Keuangan',
                'deskripsi' => 'Pelatihan manajemen keuangan untuk UMKM',
                'gambar' => 'https://images.unsplash.com/photo-1554224155-6726b3ff858f?w=400&h=400&fit=crop',
                'kategori' => 'pelatihan',
                'tanggal' => '2024-07-01',
                'views' => 95
            ],
            [
                'id' => 6,
                'judul' => 'Workshop Branding Produk',
                'deskripsi' => 'Workshop strategi branding untuk produk UMKM',
                'gambar' => 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=400&fit=crop',
                'kategori' => 'workshop',
                'tanggal' => '2024-06-25',
                'views' => 110
            ]
        ]);

        // Statistik galeri
        $stats = [
            'total_foto' => $galeri->count(),
            'total_views' => $galeri->sum('views'),
            'kategori_terpopuler' => 'Pelatihan',
            'foto_terpopuler' => $galeri->sortByDesc('views')->first()['judul']
        ];

        // Kategori
        $kategori = [
            'pelatihan' => $galeri->where('kategori', 'pelatihan')->count(),
            'workshop' => $galeri->where('kategori', 'workshop')->count(),
            'event' => $galeri->where('kategori', 'event')->count(),
            'kunjungan' => $galeri->where('kategori', 'kunjungan')->count()
        ];

        return view('admin.landing.galeri', compact('galeri', 'stats', 'kategori'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'judul' => 'required|string|max:255',
            'deskripsi' => 'required|string',
            'kategori' => 'required|in:pelatihan,workshop,event,kunjungan',
            'gambar' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
            'tanggal' => 'required|date'
        ]);

        // Logic untuk upload dan simpan foto
        // Untuk sekarang redirect dengan pesan sukses

        return redirect()->route('admin.landing.galeri')
            ->with('success', 'Foto berhasil ditambahkan ke galeri!');
    }

    public function destroy($id)
    {
        // Logic untuk hapus foto dari galeri
        // Untuk sekarang redirect dengan pesan sukses

        return redirect()->route('admin.landing.galeri')
            ->with('success', 'Foto berhasil dihapus dari galeri!');
    }
}