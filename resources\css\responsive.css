/* Responsive Utilities untuk PLUT Purworejo */

/* Mobile First Approach */
@media (max-width: 640px) {
    /* Small screens (mobile) */
    .container {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    /* Typography responsive */
    .text-4xl {
        font-size: 2rem;
        line-height: 2.5rem;
    }
    
    .text-5xl {
        font-size: 2.5rem;
        line-height: 3rem;
    }
    
    /* Button responsive */
    .tombol-utama {
        padding: 0.75rem 1.5rem;
        font-size: 0.875rem;
    }
    
    /* Card responsive */
    .kartu-konten {
        padding: 1rem;
        margin-bottom: 1rem;
    }
    
    /* Form responsive */
    .form-input {
        padding: 0.75rem;
        font-size: 0.875rem;
    }
    
    /* Navigation mobile */
    .nav-mobile {
        display: block;
    }
    
    .nav-desktop {
        display: none;
    }
    
    /* Grid responsive */
    .grid-responsive {
        grid-template-columns: repeat(1, minmax(0, 1fr));
        gap: 1rem;
    }
    
    /* Spacing mobile */
    .py-16 {
        padding-top: 3rem;
        padding-bottom: 3rem;
    }
    
    .py-20 {
        padding-top: 4rem;
        padding-bottom: 4rem;
    }
    
    /* Image responsive */
    .img-responsive {
        width: 100%;
        height: auto;
        object-fit: cover;
    }
    
    /* Table responsive */
    .table-responsive {
        overflow-x: auto;
        white-space: nowrap;
    }
    
    /* Modal responsive */
    .modal-responsive {
        margin: 1rem;
        max-width: calc(100vw - 2rem);
    }
}

@media (min-width: 641px) and (max-width: 768px) {
    /* Medium screens (tablet) */
    .container {
        padding-left: 1.5rem;
        padding-right: 1.5rem;
    }
    
    .grid-responsive {
        grid-template-columns: repeat(2, minmax(0, 1fr));
        gap: 1.5rem;
    }
    
    .nav-mobile {
        display: none;
    }
    
    .nav-desktop {
        display: flex;
    }
}

@media (min-width: 769px) and (max-width: 1024px) {
    /* Large screens (small desktop) */
    .grid-responsive {
        grid-template-columns: repeat(3, minmax(0, 1fr));
        gap: 2rem;
    }
}

@media (min-width: 1025px) {
    /* Extra large screens (desktop) */
    .grid-responsive {
        grid-template-columns: repeat(4, minmax(0, 1fr));
        gap: 2rem;
    }
}

/* Utility classes untuk responsivitas */
.hide-mobile {
    display: none;
}

@media (min-width: 768px) {
    .hide-mobile {
        display: block;
    }
    
    .show-mobile {
        display: none;
    }
}

.show-mobile {
    display: block;
}

/* Flexbox responsive utilities */
.flex-responsive {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

@media (min-width: 640px) {
    .flex-responsive {
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
    }
}

/* Text responsive utilities */
.text-responsive {
    font-size: 0.875rem;
    line-height: 1.25rem;
}

@media (min-width: 640px) {
    .text-responsive {
        font-size: 1rem;
        line-height: 1.5rem;
    }
}

@media (min-width: 768px) {
    .text-responsive {
        font-size: 1.125rem;
        line-height: 1.75rem;
    }
}

/* Padding responsive utilities */
.padding-responsive {
    padding: 1rem;
}

@media (min-width: 640px) {
    .padding-responsive {
        padding: 1.5rem;
    }
}

@media (min-width: 768px) {
    .padding-responsive {
        padding: 2rem;
    }
}

/* Margin responsive utilities */
.margin-responsive {
    margin: 1rem 0;
}

@media (min-width: 640px) {
    .margin-responsive {
        margin: 1.5rem 0;
    }
}

@media (min-width: 768px) {
    .margin-responsive {
        margin: 2rem 0;
    }
}

/* Gallery responsive */
.gallery-responsive {
    display: grid;
    grid-template-columns: repeat(1, minmax(0, 1fr));
    gap: 1rem;
}

@media (min-width: 640px) {
    .gallery-responsive {
        grid-template-columns: repeat(2, minmax(0, 1fr));
        gap: 1.5rem;
    }
}

@media (min-width: 768px) {
    .gallery-responsive {
        grid-template-columns: repeat(3, minmax(0, 1fr));
        gap: 2rem;
    }
}

@media (min-width: 1024px) {
    .gallery-responsive {
        grid-template-columns: repeat(4, minmax(0, 1fr));
        gap: 2rem;
    }
}

/* Form responsive */
.form-responsive {
    display: grid;
    grid-template-columns: repeat(1, minmax(0, 1fr));
    gap: 1rem;
}

@media (min-width: 768px) {
    .form-responsive {
        grid-template-columns: repeat(2, minmax(0, 1fr));
        gap: 1.5rem;
    }
}

/* Card responsive */
.card-responsive {
    padding: 1rem;
    margin-bottom: 1rem;
}

@media (min-width: 640px) {
    .card-responsive {
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }
}

@media (min-width: 768px) {
    .card-responsive {
        padding: 2rem;
        margin-bottom: 2rem;
    }
}

/* Navigation responsive */
.nav-responsive {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

@media (min-width: 768px) {
    .nav-responsive {
        flex-direction: row;
        gap: 2rem;
        align-items: center;
    }
}

/* Button responsive */
.btn-responsive {
    width: 100%;
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
}

@media (min-width: 640px) {
    .btn-responsive {
        width: auto;
        padding: 0.75rem 1.5rem;
        font-size: 1rem;
    }
}

/* Hero section responsive */
.hero-responsive {
    padding: 3rem 1rem;
    text-align: center;
}

@media (min-width: 640px) {
    .hero-responsive {
        padding: 4rem 1.5rem;
    }
}

@media (min-width: 768px) {
    .hero-responsive {
        padding: 5rem 2rem;
    }
}

/* Statistics responsive */
.stats-responsive {
    display: grid;
    grid-template-columns: repeat(1, minmax(0, 1fr));
    gap: 1rem;
    text-align: center;
}

@media (min-width: 640px) {
    .stats-responsive {
        grid-template-columns: repeat(2, minmax(0, 1fr));
        gap: 1.5rem;
    }
}

@media (min-width: 768px) {
    .stats-responsive {
        grid-template-columns: repeat(4, minmax(0, 1fr));
        gap: 2rem;
    }
}

/* Sidebar responsive */
.sidebar-responsive {
    order: 2;
}

@media (min-width: 1024px) {
    .sidebar-responsive {
        order: 1;
    }
}

.main-content-responsive {
    order: 1;
}

@media (min-width: 1024px) {
    .main-content-responsive {
        order: 2;
    }
}

/* Video responsive */
.video-responsive {
    position: relative;
    padding-bottom: 56.25%; /* 16:9 aspect ratio */
    height: 0;
    overflow: hidden;
}

.video-responsive iframe,
.video-responsive object,
.video-responsive embed {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

/* Map responsive */
.map-responsive {
    width: 100%;
    height: 300px;
}

@media (min-width: 768px) {
    .map-responsive {
        height: 400px;
    }
}

@media (min-width: 1024px) {
    .map-responsive {
        height: 500px;
    }
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    .print-only {
        display: block !important;
    }
    
    body {
        font-size: 12pt;
        line-height: 1.4;
        color: black;
        background: white;
    }
    
    .container {
        max-width: none;
        margin: 0;
        padding: 0;
    }
    
    .kartu-konten {
        box-shadow: none;
        border: 1px solid #ccc;
        page-break-inside: avoid;
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .kartu-konten {
        border: 2px solid #000;
    }
    
    .tombol-utama {
        border: 2px solid #000;
    }
    
    a {
        text-decoration: underline;
    }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .dark-mode-support {
        background-color: #1a1a1a;
        color: #ffffff;
    }
    
    .kartu-konten {
        background-color: #2d2d2d;
        border-color: #404040;
    }
}