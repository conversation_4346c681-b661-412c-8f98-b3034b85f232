<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PLUT Admin - @yield('title')</title>
    @vite(['resources/css/app.css', 'resources/js/app.js']) {{-- Pastikan Vite berjalan --}}
</head>
<body class="font-sans antialiased bg-gray-100">

    <div class="flex min-h-screen">

        <!-- Sidebar -->
        <aside class="w-64 bg-white shadow-lg">
            <div class="p-4 text-xl font-bold text-center border-b">
                PLUT Admin
            </div>

            <nav class="p-4 space-y-2 text-gray-700">
                <!-- Dashboard -->
                <a href="{{ route('admin.dashboard') }}"
                   class="block px-4 py-2 rounded hover:bg-gray-200 {{ request()->routeIs('admin.dashboard') ? 'bg-gray-100 font-semibold' : '' }}">
                   Dashboard
                </a>

                <!-- Manajemen UMKM -->
                <a href="{{ route('admin.umkm.index') }}"
                   class="block px-4 py-2 rounded hover:bg-gray-200 {{ request()->routeIs('admin.umkm.*') ? 'bg-gray-100 font-semibold' : '' }}">
                   Data UMKM
                </a>

                <!-- Pelatihan -->
                <a href="{{ route('admin.pelatihan.index') }}"
                   class="block px-4 py-2 rounded hover:bg-gray-200 {{ request()->routeIs('admin.pelatihan.*') ? 'bg-gray-100 font-semibold' : '' }}">
                   Pelatihan
                </a>

                <!-- Berita -->
                <a href="{{ route('admin.berita.index') }}"
                   class="block px-4 py-2 rounded hover:bg-gray-200 {{ request()->routeIs('admin.berita.*') ? 'bg-gray-100 font-semibold' : '' }}">
                   Berita
                </a>

                <!-- Galeri -->
                <a href="{{ route('admin.galeri.index') }}"
                   class="block px-4 py-2 rounded hover:bg-gray-200 {{ request()->routeIs('admin.galeri.*') ? 'bg-gray-100 font-semibold' : '' }}">
                   Galeri
                </a>

                <!-- Pengguna -->
                <a href="{{ route('admin.users.index') }}"
                   class="block px-4 py-2 rounded hover:bg-gray-200 {{ request()->routeIs('admin.users.*') ? 'bg-gray-100 font-semibold' : '' }}">
                   Pengguna
                </a>

                <!-- Logout -->
                <a href="{{ route('logout') }}"
                   class="block px-4 py-2 text-red-600 rounded hover:bg-red-100">
                   Logout
                </a>
            </nav>
        </aside>

        <!-- Main content -->
        <main class="flex-1 p-6">
            <!-- Header -->
            <header class="pb-4 mb-6 border-b">
                <h1 class="text-2xl font-semibold">@yield('title')</h1>
            </header>

            <!-- Page Content -->
            @yield('content')
        </main>
    </div>

</body>
</ht
