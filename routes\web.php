<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Admin\UmkmController;
use App\Http\Controllers\Admin\PelatihanController as AdminPelatihanController;
use App\Http\Controllers\Admin\BeritaController as AdminBeritaController;
use App\Http\Controllers\Admin\GaleriController as AdminGaleriController;
use App\Http\Controllers\Admin\UserController;
use App\Http\Controllers\Frontend\PelatihanController;
use App\Http\Controllers\Frontend\BeritaController;

/*
|--------------------------------------------------------------------------
| ROUTE PUBLIK (FRONTEND)
|--------------------------------------------------------------------------
*/

// Halaman statis
Route::view('/', 'landing.beranda')->name('beranda');
Route::view('/profil', 'landing.profil')->name('profil');
Route::get('/galeri', [\App\Http\Controllers\Frontend\GaleriController::class, 'index'])->name('galeri');
Route::get('/peta', [\App\Http\Controllers\Frontend\PetaController::class, 'index'])->name('peta');
Route::get('/kontak', [\App\Http\Controllers\Frontend\KontakController::class, 'index'])->name('kontak');
Route::get('/login', [\App\Http\Controllers\Frontend\LoginController::class, 'index'])->name('login');
Route::get('/registrasi', [\App\Http\Controllers\Frontend\RegistrasiController::class, 'index'])->name('registrasi');

// Berita publik (Frontend\BeritaController)
Route::get('/berita', [BeritaController::class, 'index'])->name('berita');
Route::get('/berita/{slug}', [BeritaController::class, 'show'])->name('berita.detail');

// Pelatihan publik (Frontend\PelatihanController)
Route::get('/pelatihan', [PelatihanController::class, 'index'])->name('pelatihan');
Route::get('/pelatihan/{slug}', [PelatihanController::class, 'show'])->name('pelatihan.detail');
Route::view('/pelatihan/riwayat', 'riwayat-pelatihan')->name('pelatihan.riwayat');

/*
|--------------------------------------------------------------------------
| ROUTE ADMIN (DASHBOARD BACKEND)
|--------------------------------------------------------------------------
| Prefix: /admin
| Nama route: admin.*
*/

Route::prefix('admin')->name('admin.')->group(function () {
    // Dashboard
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

    // Landing Page Management
    Route::prefix('landing')->name('landing.')->group(function () {
        Route::get('/beranda', [\App\Http\Controllers\Admin\Landing\BerandaController::class, 'index'])->name('beranda');
        Route::post('/beranda', [\App\Http\Controllers\Admin\Landing\BerandaController::class, 'update'])->name('beranda.update');
        
        Route::get('/profil', [\App\Http\Controllers\Admin\Landing\ProfilController::class, 'index'])->name('profil');
        Route::post('/profil', [\App\Http\Controllers\Admin\Landing\ProfilController::class, 'update'])->name('profil.update');
        
        Route::get('/berita', [\App\Http\Controllers\Admin\Landing\BeritaController::class, 'index'])->name('berita');
        Route::get('/berita/create', [\App\Http\Controllers\Admin\Landing\BeritaController::class, 'create'])->name('berita.create');
        Route::post('/berita', [\App\Http\Controllers\Admin\Landing\BeritaController::class, 'store'])->name('berita.store');
        Route::get('/berita/{id}/edit', [\App\Http\Controllers\Admin\Landing\BeritaController::class, 'edit'])->name('berita.edit');
        Route::put('/berita/{id}', [\App\Http\Controllers\Admin\Landing\BeritaController::class, 'update'])->name('berita.update');
        Route::delete('/berita/{id}', [\App\Http\Controllers\Admin\Landing\BeritaController::class, 'destroy'])->name('berita.destroy');
        
        Route::get('/pelatihan', [\App\Http\Controllers\Admin\Landing\PelatihanController::class, 'index'])->name('pelatihan');
        Route::get('/pelatihan/create', [\App\Http\Controllers\Admin\Landing\PelatihanController::class, 'create'])->name('pelatihan.create');
        Route::post('/pelatihan', [\App\Http\Controllers\Admin\Landing\PelatihanController::class, 'store'])->name('pelatihan.store');
        Route::get('/pelatihan/{id}/edit', [\App\Http\Controllers\Admin\Landing\PelatihanController::class, 'edit'])->name('pelatihan.edit');
        Route::put('/pelatihan/{id}', [\App\Http\Controllers\Admin\Landing\PelatihanController::class, 'update'])->name('pelatihan.update');
        Route::delete('/pelatihan/{id}', [\App\Http\Controllers\Admin\Landing\PelatihanController::class, 'destroy'])->name('pelatihan.destroy');
        
        Route::get('/galeri', [\App\Http\Controllers\Admin\Landing\GaleriController::class, 'index'])->name('galeri');
        Route::post('/galeri', [\App\Http\Controllers\Admin\Landing\GaleriController::class, 'store'])->name('galeri.store');
        Route::delete('/galeri/{id}', [\App\Http\Controllers\Admin\Landing\GaleriController::class, 'destroy'])->name('galeri.destroy');
    });

    // UMKM Management
    Route::prefix('umkm')->name('umkm.')->group(function () {
        Route::get('/', [\App\Http\Controllers\Admin\UmkmController::class, 'index'])->name('index');
        Route::get('/create', [\App\Http\Controllers\Admin\UmkmController::class, 'create'])->name('create');
        Route::post('/', [\App\Http\Controllers\Admin\UmkmController::class, 'store'])->name('store');
        Route::get('/{id}', [\App\Http\Controllers\Admin\UmkmController::class, 'show'])->name('show');
        Route::get('/{id}/edit', [\App\Http\Controllers\Admin\UmkmController::class, 'edit'])->name('edit');
        Route::put('/{id}', [\App\Http\Controllers\Admin\UmkmController::class, 'update'])->name('update');
        Route::delete('/{id}', [\App\Http\Controllers\Admin\UmkmController::class, 'destroy'])->name('destroy');
        
        Route::get('/pendaftaran', [\App\Http\Controllers\Admin\UmkmController::class, 'pendaftaran'])->name('pendaftaran');
        Route::get('/kategori', [\App\Http\Controllers\Admin\UmkmController::class, 'kategori'])->name('kategori');
        Route::get('/verifikasi', [\App\Http\Controllers\Admin\UmkmController::class, 'verifikasi'])->name('verifikasi');
        Route::post('/{id}/verify', [\App\Http\Controllers\Admin\UmkmController::class, 'verify'])->name('verify');
        Route::post('/{id}/reject', [\App\Http\Controllers\Admin\UmkmController::class, 'reject'])->name('reject');
    });

    // User Management
    Route::prefix('users')->name('users.')->group(function () {
        Route::get('/', [\App\Http\Controllers\Admin\UserController::class, 'index'])->name('index');
        Route::get('/create', [\App\Http\Controllers\Admin\UserController::class, 'create'])->name('create');
        Route::post('/', [\App\Http\Controllers\Admin\UserController::class, 'store'])->name('store');
        Route::get('/{id}/edit', [\App\Http\Controllers\Admin\UserController::class, 'edit'])->name('edit');
        Route::put('/{id}', [\App\Http\Controllers\Admin\UserController::class, 'update'])->name('update');
        Route::delete('/{id}', [\App\Http\Controllers\Admin\UserController::class, 'destroy'])->name('destroy');
        
        Route::get('/admin', [\App\Http\Controllers\Admin\UserController::class, 'admin'])->name('admin');
        Route::get('/roles', [\App\Http\Controllers\Admin\UserController::class, 'roles'])->name('roles');
    });

    // Reports
    Route::prefix('laporan')->name('laporan.')->group(function () {
        Route::get('/umkm', [\App\Http\Controllers\Admin\LaporanController::class, 'umkm'])->name('umkm');
        Route::get('/pelatihan', [\App\Http\Controllers\Admin\LaporanController::class, 'pelatihan'])->name('pelatihan');
        Route::get('/statistik', [\App\Http\Controllers\Admin\LaporanController::class, 'statistik'])->name('statistik');
    });

    // Settings
    Route::prefix('pengaturan')->name('pengaturan.')->group(function () {
        Route::get('/umum', [\App\Http\Controllers\Admin\PengaturanController::class, 'umum'])->name('umum');
        Route::post('/umum', [\App\Http\Controllers\Admin\PengaturanController::class, 'updateUmum'])->name('umum.update');
        Route::get('/kontak', [\App\Http\Controllers\Admin\PengaturanController::class, 'kontak'])->name('kontak');
        Route::post('/kontak', [\App\Http\Controllers\Admin\PengaturanController::class, 'updateKontak'])->name('kontak.update');
        Route::get('/backup', [\App\Http\Controllers\Admin\PengaturanController::class, 'backup'])->name('backup');
        Route::post('/backup', [\App\Http\Controllers\Admin\PengaturanController::class, 'createBackup'])->name('backup.create');
    });
});

/*
|--------------------------------------------------------------------------
| ROUTE DASHBOARD UMKM (opsional)
|--------------------------------------------------------------------------
*/

Route::view('/umkm/dashboard', 'umkm.dashboard')->name('umkm.dashboard');

/*
|--------------------------------------------------------------------------
| ROUTE LOGOUT (Dummy)
|--------------------------------------------------------------------------
*/

Route::get('/logout', function () {
    return redirect()->route('login');
})->name('logout');
